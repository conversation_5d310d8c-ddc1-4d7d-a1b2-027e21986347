import json
from typing import Dict, List, Any
from dao.database import db


class AgentService:
    """Agent服务类"""

    def __init__(self):
        self.db = db
        self.scheduler = None

    def set_scheduler(self, scheduler):
        """设置调度器引用"""
        self.scheduler = scheduler

    def get_agent_status(self) -> Dict[str, Any]:
        """获取Agent状态"""
        if self.scheduler:
            return self.scheduler.get_agent_status()
        return {'error': 'Agent调度器未初始化'}

    def manual_run_agent(self, agent_name: str) -> Dict[str, Any]:
        """手动执行Agent"""
        if self.scheduler:
            return self.scheduler.manual_run_agent(agent_name)
        return {'error': 'Agent调度器未初始化'}

    def get_execution_history(self, agent_name: str = None, limit: int = 50) -> List[Dict]:
        """获取执行历史"""
        if self.scheduler:
            return self.scheduler.get_execution_history(agent_name, limit)
        return []

    def get_agent_statistics(self) -> Dict[str, Any]:
        """获取Agent统计信息"""
        if self.scheduler:
            return self.scheduler.get_agent_statistics()
        return {}

    def get_user_recommendations(self, user_id: int, limit: int = 5) -> List[Dict]:
        """获取用户推荐"""
        try:
            sql = """
            SELECT ur.*, m.name as material_name, m.category, m.remaining_quantity, m.unit_price
            FROM user_recommendations ur
            JOIN materials m ON ur.material_id = m.id
            WHERE ur.user_id = %s
            AND m.status = 'available'
            AND m.remaining_quantity > 0
            ORDER BY ur.score DESC, ur.created_at DESC
            LIMIT %s
            """
            return self.db.execute_query(sql, (user_id, limit))
        except Exception as e:
            print(f"获取用户推荐失败: {e}")
            return []

    def get_agent_config(self) -> Dict[str, Any]:
        """获取Agent配置"""
        try:
            # 确保配置表存在
            self._ensure_config_table()
            
            sql = "SELECT * FROM agent_config"
            configs = self.db.execute_query(sql)
            
            # 转换为字典格式
            config_dict = {}
            for config in configs:
                try:
                    config_dict[config['agent_name']] = json.loads(config['config_data'])
                except json.JSONDecodeError:
                    config_dict[config['agent_name']] = {}
            
            # 如果没有配置，返回默认配置
            if not config_dict:
                config_dict = self._get_default_config()
                
            return config_dict
            
        except Exception as e:
            print(f"获取Agent配置失败: {e}")
            return self._get_default_config()

    def update_agent_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新Agent配置"""
        try:
            # 确保配置表存在
            self._ensure_config_table()
            
            for agent_name, config in config_data.items():
                # 检查配置是否存在
                check_sql = "SELECT id FROM agent_config WHERE agent_name = %s"
                existing = self.db.execute_query_one(check_sql, (agent_name,))
                
                config_json = json.dumps(config, ensure_ascii=False)
                
                if existing:
                    # 更新现有配置
                    update_sql = """
                    UPDATE agent_config 
                    SET config_data = %s, updated_at = NOW() 
                    WHERE agent_name = %s
                    """
                    self.db.execute_update(update_sql, (config_json, agent_name))
                else:
                    # 插入新配置
                    insert_sql = """
                    INSERT INTO agent_config (agent_name, config_data, created_at, updated_at)
                    VALUES (%s, %s, NOW(), NOW())
                    """
                    self.db.execute_insert(insert_sql, (agent_name, config_json))
            
            return {'success': True, 'message': '配置更新成功'}
            
        except Exception as e:
            print(f"更新Agent配置失败: {e}")
            return {'success': False, 'error': str(e)}

    def _ensure_config_table(self):
        """确保配置表存在"""
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS agent_config (
                id INT AUTO_INCREMENT PRIMARY KEY,
                agent_name VARCHAR(100) NOT NULL UNIQUE,
                config_data JSON NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_agent_name (agent_name)
            )
            """
            self.db.execute_update(create_table_sql)
        except Exception as e:
            print(f"创建配置表失败: {e}")

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'InventoryAlert': {
                'low_stock_threshold': 10,
                'schedule_time': '08:00',
                'interval': 'daily',
                'enabled': True
            },
            'SmartRecommendation': {
                'max_recommendations': 5,
                'similarity_threshold': 0.1,
                'schedule_time': '22:00',
                'interval': 'daily',
                'enabled': True
            },
            'DataAnalysis': {
                'analysis_period_months': 3,
                'schedule_time': 'monday.09:00',
                'interval': 'weekly',
                'enabled': True
            }
        }

    def get_low_stock_alerts(self, limit: int = 20) -> List[Dict]:
        """获取低库存预警"""
        try:
            sql = """
            SELECT id, name, category, remaining_quantity, quantity, 
                   ROUND((remaining_quantity / quantity) * 100, 1) as stock_percentage
            FROM materials 
            WHERE remaining_quantity <= 10 
            AND status = 'available'
            ORDER BY remaining_quantity ASC
            LIMIT %s
            """
            return self.db.execute_query(sql, (limit,))
        except Exception as e:
            print(f"获取低库存预警失败: {e}")
            return []

    def get_usage_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取使用统计"""
        try:
            # 按类别统计
            category_sql = """
            SELECT m.category, COUNT(*) as request_count, SUM(mr.quantity) as total_quantity
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            GROUP BY m.category
            ORDER BY total_quantity DESC
            """
            category_stats = self.db.execute_query(category_sql, (days,))
            
            # 按科室统计
            department_sql = """
            SELECT d.name as department_name, COUNT(*) as request_count, SUM(mr.quantity) as total_quantity
            FROM material_requests mr
            JOIN users u ON mr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            GROUP BY d.id, d.name
            ORDER BY total_quantity DESC
            """
            department_stats = self.db.execute_query(department_sql, (days,))
            
            # 总体统计
            total_sql = """
            SELECT COUNT(*) as total_requests, SUM(quantity) as total_quantity
            FROM material_requests
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            """
            total_stats = self.db.execute_query_one(total_sql, (days,))
            
            return {
                'category_statistics': category_stats,
                'department_statistics': department_stats,
                'total_statistics': total_stats or {'total_requests': 0, 'total_quantity': 0}
            }
            
        except Exception as e:
            print(f"获取使用统计失败: {e}")
            return {
                'category_statistics': [],
                'department_statistics': [],
                'total_statistics': {'total_requests': 0, 'total_quantity': 0}
            }
