Metadata-Version: 2.1
Name: pycparser
Version: 2.21
Summary: C parser in Python
Home-page: https://github.com/eliben/pycparser
Author: <PERSON>
Author-email: <EMAIL>
Maintainer: <PERSON>ky
License: BSD
Platform: Cross Platform
Classifier: Development Status :: 5 - Production/Stable
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Requires-Python: >=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*


pycparser is a complete parser of the C language, written in
pure Python using the PLY parsing library.
It parses C code into an AST and can serve as a front-end for
C compilers or analysis tools.


