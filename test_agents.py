#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Agent系统
"""

from agents.inventory_alert_agent import InventoryAlertAgent
from agents.smart_recommendation_agent import SmartRecommendationAgent
from agents.data_analysis_agent import DataAnalysisAgent
from dao.database import db

def test_inventory_alert():
    """测试库存预警Agent"""
    print("=" * 50)
    print("测试库存预警Agent")
    print("=" * 50)
    
    agent = InventoryAlertAgent()
    result = agent.execute()
    
    print(f"执行结果: {result}")
    print()

def test_smart_recommendation():
    """测试智能推荐Agent"""
    print("=" * 50)
    print("测试智能推荐Agent")
    print("=" * 50)
    
    agent = SmartRecommendationAgent()
    result = agent.execute()
    
    print(f"执行结果: {result}")
    print()

def test_data_analysis():
    """测试数据分析Agent"""
    print("=" * 50)
    print("测试数据分析Agent")
    print("=" * 50)
    
    agent = DataAnalysisAgent()
    result = agent.execute()
    
    print(f"执行结果: {result}")
    print()

def create_test_data():
    """创建测试数据"""
    print("=" * 50)
    print("创建测试数据")
    print("=" * 50)
    
    try:
        # 创建一些低库存的物资用于测试库存预警
        update_sql = """
        UPDATE materials 
        SET remaining_quantity = 5 
        WHERE id IN (
            SELECT id FROM (
                SELECT id FROM materials 
                WHERE remaining_quantity > 10 
                LIMIT 3
            ) as temp
        )
        """
        db.execute_update(update_sql)
        print("✅ 已创建低库存测试数据")
        
        # 创建一些物资申请记录用于测试推荐系统
        # 获取用户和物资
        users = db.execute_query("SELECT id, department_id FROM users LIMIT 5")
        materials = db.execute_query("SELECT id FROM materials WHERE status = 'available' LIMIT 10")
        
        if users and materials:
            import random
            from datetime import datetime, timedelta
            
            # 创建一些历史申请记录
            for i in range(20):
                user = random.choice(users)
                material = random.choice(materials)
                quantity = random.randint(1, 5)
                
                # 随机生成过去30天内的时间
                days_ago = random.randint(1, 30)
                created_at = datetime.now() - timedelta(days=days_ago)
                
                insert_sql = """
                INSERT INTO material_requests (user_id, material_id, quantity, reason, status, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                db.execute_insert(insert_sql, (
                    user['id'],
                    material['id'],
                    quantity,
                    f'测试申请记录 {i+1}',
                    'approved',
                    created_at
                ))
            
            print("✅ 已创建物资申请测试数据")
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")

def show_agent_results():
    """显示Agent执行结果"""
    print("=" * 50)
    print("Agent执行结果")
    print("=" * 50)
    
    try:
        # 显示通知
        notifications_sql = """
        SELECT n.*, u.real_name 
        FROM notifications n
        JOIN users u ON n.user_id = u.id
        ORDER BY n.created_at DESC
        LIMIT 10
        """
        notifications = db.execute_query(notifications_sql)
        
        if notifications:
            print("📢 最新通知:")
            for notif in notifications:
                print(f"  - {notif['title']} (发送给: {notif['real_name']})")
                print(f"    {notif['message']}")
                print(f"    时间: {notif['created_at']}")
                print()
        
        # 显示推荐
        recommendations_sql = """
        SELECT ur.*, m.name as material_name, u.real_name
        FROM user_recommendations ur
        JOIN materials m ON ur.material_id = m.id
        JOIN users u ON ur.user_id = u.id
        ORDER BY ur.created_at DESC
        LIMIT 10
        """
        recommendations = db.execute_query(recommendations_sql)
        
        if recommendations:
            print("💡 最新推荐:")
            for rec in recommendations:
                print(f"  - 为 {rec['real_name']} 推荐: {rec['material_name']}")
                print(f"    推荐度: {rec['score']:.2f}")
                print(f"    理由: {rec['reason']}")
                print(f"    时间: {rec['created_at']}")
                print()
        
        # 显示执行日志
        logs_sql = """
        SELECT * FROM agent_execution_logs
        ORDER BY executed_at DESC
        LIMIT 10
        """
        logs = db.execute_query(logs_sql)
        
        if logs:
            print("📊 执行日志:")
            for log in logs:
                print(f"  - {log['agent_name']}: {log['status']}")
                print(f"    耗时: {log['execution_time']:.2f}秒")
                print(f"    时间: {log['executed_at']}")
                print()
                
    except Exception as e:
        print(f"❌ 显示结果失败: {e}")

def main():
    """主函数"""
    print("Agent系统测试工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 创建测试数据")
        print("2. 测试库存预警Agent")
        print("3. 测试智能推荐Agent")
        print("4. 测试数据分析Agent")
        print("5. 运行所有Agent")
        print("6. 查看Agent结果")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-6): ").strip()
        
        if choice == '0':
            print("退出测试工具")
            break
        elif choice == '1':
            create_test_data()
        elif choice == '2':
            test_inventory_alert()
        elif choice == '3':
            test_smart_recommendation()
        elif choice == '4':
            test_data_analysis()
        elif choice == '5':
            test_inventory_alert()
            test_smart_recommendation()
            test_data_analysis()
        elif choice == '6':
            show_agent_results()
        else:
            print("无效选择，请重新输入")

if __name__ == '__main__':
    main()
