#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Agent Web接口
"""

import requests
import json

BASE_URL = 'http://localhost:5000'

def login_as_admin():
    """以管理员身份登录"""
    session = requests.Session()
    
    # 获取登录页面
    response = session.get(f'{BASE_URL}/auth/login')
    print(f"获取登录页面: {response.status_code}")
    
    # 登录
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = session.post(f'{BASE_URL}/auth/login', data=login_data)
    print(f"登录请求: {response.status_code}")
    
    if response.status_code == 200 and 'login' not in response.url:
        print("✅ 登录成功")
        return session
    else:
        print("❌ 登录失败")
        return None

def test_agent_dashboard(session):
    """测试Agent监控面板"""
    print("\n" + "="*50)
    print("测试Agent监控面板")
    print("="*50)
    
    response = session.get(f'{BASE_URL}/agent/dashboard')
    print(f"访问监控面板: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ 监控面板访问成功")
        # 检查页面内容
        if 'Agent监控面板' in response.text:
            print("✅ 页面内容正确")
        else:
            print("❌ 页面内容异常")
    else:
        print(f"❌ 监控面板访问失败: {response.status_code}")

def test_agent_api_status(session):
    """测试Agent状态API"""
    print("\n" + "="*50)
    print("测试Agent状态API")
    print("="*50)
    
    response = session.get(f'{BASE_URL}/agent/api/status')
    print(f"获取Agent状态: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print("✅ Agent状态API正常")
            print(f"Agent数量: {len(data)}")
            for agent_name, status in data.items():
                print(f"  - {agent_name}: {status.get('status', 'unknown')}")
        except json.JSONDecodeError:
            print("❌ 响应不是有效的JSON")
    else:
        print(f"❌ Agent状态API失败: {response.status_code}")

def test_manual_run_agent(session, agent_name):
    """测试手动运行Agent"""
    print(f"\n测试手动运行Agent: {agent_name}")
    print("-" * 30)
    
    response = session.post(f'{BASE_URL}/agent/api/run/{agent_name}')
    print(f"手动运行{agent_name}: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"✅ {agent_name}执行成功")
            print(f"执行结果: {data.get('status', 'unknown')}")
            if 'error' in data:
                print(f"错误信息: {data['error']}")
        except json.JSONDecodeError:
            print("❌ 响应不是有效的JSON")
    else:
        print(f"❌ {agent_name}执行失败: {response.status_code}")

def test_notifications(session):
    """测试通知页面"""
    print("\n" + "="*50)
    print("测试通知页面")
    print("="*50)
    
    response = session.get(f'{BASE_URL}/agent/notifications')
    print(f"访问通知页面: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ 通知页面访问成功")
        if '系统通知' in response.text:
            print("✅ 页面内容正确")
        else:
            print("❌ 页面内容异常")
    else:
        print(f"❌ 通知页面访问失败: {response.status_code}")

def test_recommendations(session):
    """测试推荐页面"""
    print("\n" + "="*50)
    print("测试推荐页面")
    print("="*50)
    
    response = session.get(f'{BASE_URL}/agent/recommendations')
    print(f"访问推荐页面: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ 推荐页面访问成功")
        if '智能推荐' in response.text:
            print("✅ 页面内容正确")
        else:
            print("❌ 页面内容异常")
    else:
        print(f"❌ 推荐页面访问失败: {response.status_code}")

def test_config_page(session):
    """测试配置页面"""
    print("\n" + "="*50)
    print("测试配置页面")
    print("="*50)
    
    response = session.get(f'{BASE_URL}/agent/config')
    print(f"访问配置页面: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ 配置页面访问成功")
        if 'Agent配置管理' in response.text:
            print("✅ 页面内容正确")
        else:
            print("❌ 页面内容异常")
    else:
        print(f"❌ 配置页面访问失败: {response.status_code}")

def test_config_api(session):
    """测试配置API"""
    print("\n" + "="*50)
    print("测试配置API")
    print("="*50)
    
    # 获取配置
    response = session.get(f'{BASE_URL}/agent/api/config')
    print(f"获取配置: {response.status_code}")
    
    if response.status_code == 200:
        try:
            config = response.json()
            print("✅ 配置API正常")
            print(f"配置项数量: {len(config)}")
            for agent_name, agent_config in config.items():
                print(f"  - {agent_name}: {agent_config.get('enabled', False)}")
        except json.JSONDecodeError:
            print("❌ 响应不是有效的JSON")
    else:
        print(f"❌ 配置API失败: {response.status_code}")

def main():
    """主函数"""
    print("Agent Web接口测试工具")
    print("=" * 50)
    
    # 登录
    session = login_as_admin()
    if not session:
        print("无法登录，测试终止")
        return
    
    # 测试各个功能
    test_agent_dashboard(session)
    test_agent_api_status(session)
    test_notifications(session)
    test_recommendations(session)
    test_config_page(session)
    test_config_api(session)
    
    # 测试手动运行Agent
    print("\n" + "="*50)
    print("测试手动运行Agent")
    print("="*50)
    
    agents = ['InventoryAlert', 'SmartRecommendation', 'DataAnalysis']
    for agent in agents:
        test_manual_run_agent(session, agent)
    
    print("\n" + "="*50)
    print("测试完成")
    print("="*50)

if __name__ == '__main__':
    main()
