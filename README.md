# 物资管理系统

## 项目简介

这是一个基于Flask框架开发的物资管理系统，主要用于企业内部的物资分配、申请和管理。系统支持固定资产和耗材的管理，提供完整的物资生命周期管理功能。

## 主要功能

### 用户管理
- 用户登录/登出
- 角色管理（管理员/普通员工）
- 用户信息管理

### 物资管理
- 物资信息录入（固定资产/耗材）
- 物资查询和详情查看
- 物资状态管理（在用/可用/报废）
- 物资分配和归还

### 申请审批
- 物资申请提交
- 申请审批流程
- 申请状态跟踪

### 报表统计
- 物资统计报表
- 科室物资分配报表
- 高级统计分析
- 数据导出（Excel/PDF）

## 技术栈

- **后端框架**: Flask 2.3.3
- **数据库**: MySQL
- **数据库连接**: PyMySQL 1.1.0
- **模板引擎**: Jinja2 3.1.2
- **文档处理**: openpyxl 3.1.2, reportlab 4.0.4
- **前端**: HTML, CSS, JavaScript, Bootstrap

## 项目结构

```
金融系统/
├── app.py                 # 应用入口文件
├── config.py             # 配置文件
├── requirements.txt      # 依赖包列表
├── database_init.sql     # 数据库初始化脚本
├── init_database.py      # 数据库初始化Python脚本
├── controllers/          # 控制器层
│   ├── auth_controller.py      # 认证控制器
│   ├── material_controller.py  # 物资控制器
│   └── report_controller.py    # 报表控制器
├── services/            # 业务逻辑层
│   ├── auth_service.py         # 认证服务
│   ├── material_service.py     # 物资服务
│   └── report_service.py       # 报表服务
├── dao/                 # 数据访问层
│   ├── database.py            # 数据库连接
│   ├── user_dao.py           # 用户数据访问
│   ├── material_dao.py       # 物资数据访问
│   ├── department_dao.py     # 科室数据访问
│   └── allocation_dao.py     # 分配记录数据访问
├── models/              # 数据模型
│   ├── user.py               # 用户模型
│   ├── material.py           # 物资模型
│   ├── department.py         # 科室模型
│   └── allocation.py         # 分配记录模型
├── templates/           # HTML模板
├── static/             # 静态资源
│   ├── css/
│   ├── js/
│   └── images/
└── test_*.py           # 测试文件
```

## 数据库设计

系统包含以下主要数据表：

- `departments`: 科室信息表
- `users`: 用户信息表
- `materials`: 物资信息表
- `material_allocations`: 物资分配记录表
- `material_requests`: 物资申请表

## 安装和运行

### 环境要求

- Python 3.8+
- MySQL 5.7+

### 1. 克隆项目

```bash
git clone <repository-url>
cd 金融系统
```

### 2. 创建虚拟环境

```bash
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/Mac
python3 -m venv venv
source venv/bin/activate
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 配置数据库

1. 确保MySQL服务已启动
2. 修改 `config.py` 中的数据库配置：
   ```python
   DB_CONFIG = {
       'host': 'localhost',
       'user': 'your_username',
       'password': 'your_password',
       'database': 'goods',
       'charset': 'utf8mb4'
   }
   ```

### 5. 初始化数据库

```bash
# 方法1: 使用SQL脚本
mysql -u root -p < database_init.sql

# 方法2: 使用Python脚本
python init_database.py
```

### 6. 启动应用

有多种方式启动应用：

**方式1: 使用启动脚本（推荐）**
```bash
# Windows批处理文件
start.bat

# 或使用PowerShell脚本
start.ps1
```

**方式2: 手动启动**
```bash
# 激活虚拟环境
venv\Scripts\activate

# 启动应用
python app.py
```

**方式3: 直接使用虚拟环境Python**
```bash
venv\Scripts\python.exe app.py
```

应用将在 `http://localhost:5000` 启动。

## 默认账户

系统初始化后会创建以下默认账户：

- **管理员账户**
  - 用户名: `admin`
  - 密码: `123456`
  - 角色: 管理员

- **普通用户账户**
  - 用户名: `zhangsan` / 密码: `123456`
  - 用户名: `lisi` / 密码: `123456`
  - 用户名: `wangwu` / 密码: `123456`
  - 用户名: `zhaoliu` / 密码: `123456`

## 项目状态

✅ **已完成功能**
- 数据库设计和初始化
- 用户认证和权限管理
- 物资管理（添加、查看、编辑）
- 物资申请和审批流程
- 报表生成和导出
- 响应式Web界面

✅ **测试数据**
- 5个科室（财务部、人事部、技术部、市场部、行政部）
- 5个测试用户（1个管理员，4个普通员工）
- 60+种物资（固定资产和消耗品）
- 20条分配记录
- 6条待审核申请

## 使用说明

### 管理员功能
1. 登录系统后进入管理员仪表板
2. 可以添加、编辑、删除物资信息
3. 审批员工的物资申请
4. 查看各种统计报表
5. 管理用户和科室信息

### 普通员工功能
1. 登录系统后查看可申请的物资
2. 提交物资申请
3. 查看申请状态
4. 查看个人物资使用记录

## 测试

项目包含多个测试文件，可以运行以下命令进行测试：

```bash
# 测试所有路由
python test_all_routes.py

# 测试特定功能
python test_dashboard.py
python test_material_service.py
python test_request_service.py
```

## 开发说明

### 添加新功能
1. 在相应的 `dao` 层添加数据访问方法
2. 在 `services` 层添加业务逻辑
3. 在 `controllers` 层添加路由处理
4. 创建相应的HTML模板
5. 编写测试用例

### 代码规范
- 使用Python PEP 8编码规范
- 函数和类添加适当的文档字符串
- 重要业务逻辑添加注释说明

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库配置信息是否正确
- 检查数据库用户权限

### 2. 依赖包安装失败
- 确保Python版本符合要求
- 使用国内镜像源：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

### 3. 页面显示异常
- 检查模板文件是否存在
- 确认静态资源路径是否正确

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 项目部署状态

### ✅ 已完成
- [x] 虚拟环境创建和配置
- [x] 依赖包安装（兼容Python 3.7）
- [x] 数据库初始化和测试数据导入
- [x] Flask应用启动和运行
- [x] Web界面可访问（http://localhost:5000）
- [x] 启动脚本创建（start.bat, start.ps1）
- [x] 系统状态检查脚本

### 📋 测试结果
- ✅ Web服务器正常运行在端口5000
- ✅ 登录页面正常显示
- ✅ 数据库表结构完整
- ✅ 测试数据已导入

### 🚀 快速启动
1. 双击 `start.bat` 或运行 `start.ps1`
2. 浏览器访问 http://localhost:5000
3. 使用管理员账户登录：admin / 123456

### 📁 重要文件
- `app.py` - 应用入口
- `config.py` - 配置文件
- `start.bat` / `start.ps1` - 启动脚本
- `system_status.py` - 系统状态检查
- `test_login.py` - 登录功能测试
- `init_database.py` - 数据库初始化

## 许可证

本项目采用MIT许可证。
