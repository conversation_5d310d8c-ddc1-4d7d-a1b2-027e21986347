#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Agent执行结果
"""

import requests
import json
import time

BASE_URL = 'http://localhost:5000'

def login_as_admin():
    """以管理员身份登录"""
    session = requests.Session()

    # 获取登录页面
    response = session.get(f'{BASE_URL}/auth/login')

    # 尝试不同的管理员账户
    admin_accounts = [
        {'username': 'admin', 'password': 'admin'},
        {'username': 'admin', 'password': '123456'},
        {'username': 'admin', 'password': 'admin123'}
    ]

    for account in admin_accounts:
        response = session.post(f'{BASE_URL}/auth/login', data=account)

        if response.status_code == 200 and 'login' not in response.url:
            return session
        elif response.status_code == 302:
            return session

    return None

def check_execution_history(session):
    """检查执行历史"""
    print("检查Agent执行历史")
    print("=" * 30)
    
    agents = ['InventoryAlert', 'SmartRecommendation', 'DataAnalysis']
    
    for agent in agents:
        print(f"\n{agent} 执行历史:")
        response = session.get(f'{BASE_URL}/agent/api/history/{agent}?limit=5')
        
        if response.status_code == 200:
            try:
                history = response.json()
                if history:
                    for record in history:
                        status = record.get('status', 'unknown')
                        executed_at = record.get('executed_at', 'unknown')
                        execution_time = record.get('execution_time', 0)
                        print(f"  - {executed_at}: {status} ({execution_time}s)")
                        
                        # 显示结果详情
                        result = record.get('result')
                        if result:
                            try:
                                result_data = json.loads(result) if isinstance(result, str) else result
                                if isinstance(result_data, dict):
                                    for key, value in result_data.items():
                                        if key != 'status':
                                            print(f"    {key}: {value}")
                            except:
                                print(f"    结果: {result}")
                else:
                    print("  无执行记录")
            except json.JSONDecodeError:
                print("  ❌ 响应格式错误")
        else:
            print(f"  ❌ 获取历史失败: {response.status_code}")

def check_recommendations(session):
    """检查推荐结果"""
    print("\n\n检查推荐结果")
    print("=" * 30)
    
    response = session.get(f'{BASE_URL}/agent/api/recommendations')
    
    if response.status_code == 200:
        try:
            recommendations = response.json()
            print(f"推荐数量: {len(recommendations)}")
            
            for i, rec in enumerate(recommendations):
                print(f"\n推荐 {i+1}:")
                print(f"  物资: {rec.get('material_name', 'unknown')}")
                print(f"  类别: {rec.get('category', 'unknown')}")
                print(f"  理由: {rec.get('reason', 'unknown')}")
                print(f"  评分: {rec.get('score', 0)}")
                print(f"  类型: {rec.get('type', 'unknown')}")
                
        except json.JSONDecodeError:
            print("❌ 推荐数据格式错误")
    else:
        print(f"❌ 获取推荐失败: {response.status_code}")

def check_notifications(session):
    """检查通知"""
    print("\n\n检查系统通知")
    print("=" * 30)
    
    response = session.get(f'{BASE_URL}/agent/notifications')
    
    if response.status_code == 200:
        print("✅ 通知页面访问成功")
        
        # 检查未读通知数量
        response = session.get(f'{BASE_URL}/agent/api/notifications/unread_count')
        if response.status_code == 200:
            try:
                data = response.json()
                count = data.get('count', 0)
                print(f"未读通知数量: {count}")
            except:
                print("无法获取未读通知数量")
    else:
        print(f"❌ 通知页面访问失败: {response.status_code}")

def main():
    """主函数"""
    print("Agent执行结果检查工具")
    print("=" * 50)
    
    # 登录
    session = login_as_admin()
    if not session:
        print("无法登录")
        return
    
    print("✅ 登录成功")
    
    # 等待Agent执行完成
    print("\n等待Agent执行完成...")
    time.sleep(10)
    
    # 检查执行历史
    check_execution_history(session)
    
    # 检查推荐结果
    check_recommendations(session)
    
    # 检查通知
    check_notifications(session)
    
    print("\n" + "=" * 50)
    print("检查完成")
    print("=" * 50)

if __name__ == '__main__':
    main()
