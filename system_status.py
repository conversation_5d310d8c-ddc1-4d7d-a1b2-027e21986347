#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统状态检查脚本
"""

import requests
import pymysql
from config import Config

def check_database():
    """检查数据库连接"""
    try:
        connection = pymysql.connect(**Config.DB_CONFIG)
        cursor = connection.cursor()
        
        print("✅ 数据库连接正常")
        
        # 检查表是否存在
        tables = ['departments', 'users', 'materials', 'material_allocations', 'material_requests']
        cursor.execute("SHOW TABLES")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        for table in tables:
            if table in existing_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ {table} 表存在，记录数: {count}")
            else:
                print(f"❌ {table} 表不存在")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def check_web_server():
    """检查Web服务器"""
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器运行正常")
            print(f"   状态码: {response.status_code}")
            return True
        else:
            print(f"❌ Web服务器响应异常，状态码: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Web服务器未启动或无法连接")
        return False
    except Exception as e:
        print(f"❌ Web服务器检查失败: {e}")
        return False

def main():
    print("物资管理系统状态检查")
    print("=" * 50)
    
    db_ok = check_database()
    web_ok = check_web_server()
    
    print("\n" + "=" * 50)
    if db_ok and web_ok:
        print("🎉 系统运行正常！")
        print("   访问地址: http://localhost:5000")
        print("   管理员账户: admin / 123456")
    else:
        print("⚠️  系统存在问题，请检查配置")

if __name__ == '__main__':
    main()
