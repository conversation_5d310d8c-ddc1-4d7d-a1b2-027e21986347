#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前用户信息
"""

from dao.database import Database

def check_users():
    """检查用户信息"""
    db = Database()
    
    print("=== 用户信息 ===")
    
    try:
        # 检查用户表结构
        sql = "DESCRIBE users"
        columns = db.execute_query(sql)
        print("用户表结构:")
        for col in columns:
            print(f"  {col['Field']}: {col['Type']}")
        
        print("\n用户数据:")
        sql = "SELECT * FROM users"
        users = db.execute_query(sql)
        
        for user in users:
            print(f"  ID: {user['id']}, 用户名: {user['username']}, 角色: {user['role']}")
            
        # 检查admin用户的推荐
        print("\n=== admin用户推荐 ===")
        sql = """
        SELECT ur.*, m.name as material_name, m.category, m.remaining_quantity, m.unit_price
        FROM user_recommendations ur
        JOIN materials m ON ur.material_id = m.id
        WHERE ur.user_id = 1
        AND m.status = 'available'
        AND m.remaining_quantity > 0
        ORDER BY ur.score DESC, ur.created_at DESC
        """
        admin_recs = db.execute_query(sql)
        print(f"admin用户推荐数量: {len(admin_recs)}")
        
        for rec in admin_recs:
            print(f"  物资: {rec['material_name']}, 评分: {rec['score']}, 类型: {rec['type']}")
            
    except Exception as e:
        print(f"查询失败: {e}")
        import traceback
        traceback.print_exc()

def generate_admin_recommendations():
    """为admin用户生成推荐"""
    print("\n=== 为admin用户生成推荐 ===")
    
    db = Database()
    
    try:
        # 为admin用户添加一些推荐
        recommendations = [
            (1, 1, 0.85, 'personal', '基于您的管理员角色推荐'),  # 联想ThinkPad笔记本
            (1, 2, 0.80, 'personal', '显示器对管理工作很重要'),  # 戴尔显示器
            (1, 6, 0.75, 'department', '打印机是办公必需品'),  # 激光打印机
            (1, 9, 0.70, 'department', '投影仪适合会议使用'),  # 投影仪
            (1, 7, 0.65, 'global', '办公桌使用频率较高'),  # 办公桌
        ]
        
        # 先删除现有推荐
        delete_sql = "DELETE FROM user_recommendations WHERE user_id = 1"
        db.execute_update(delete_sql)
        
        # 插入新推荐
        insert_sql = """
        INSERT INTO user_recommendations (user_id, material_id, score, type, reason, created_at)
        VALUES (%s, %s, %s, %s, %s, NOW())
        """
        
        for rec in recommendations:
            db.execute_update(insert_sql, rec)
        
        print("✅ admin用户推荐生成成功")
        
        # 验证推荐
        check_sql = """
        SELECT ur.*, m.name as material_name
        FROM user_recommendations ur
        JOIN materials m ON ur.material_id = m.id
        WHERE ur.user_id = 1
        ORDER BY ur.score DESC
        """
        
        new_recs = db.execute_query(check_sql)
        print(f"新生成的推荐数量: {len(new_recs)}")
        
        for rec in new_recs:
            print(f"  物资: {rec['material_name']}, 评分: {rec['score']}, 理由: {rec['reason']}")
            
    except Exception as e:
        print(f"生成推荐失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_users()
    generate_admin_recommendations()
