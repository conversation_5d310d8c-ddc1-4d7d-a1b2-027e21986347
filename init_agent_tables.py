#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化Agent相关数据表
"""

from dao.database import db

def init_agent_tables():
    """初始化Agent相关表"""
    
    # 通知表
    notifications_sql = """
    CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('user', 'admin', 'system') DEFAULT 'user',
        data JSON,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_is_read (is_read),
        INDEX idx_created_at (created_at)
    )
    """
    
    # Agent执行日志表
    execution_logs_sql = """
    CREATE TABLE IF NOT EXISTS agent_execution_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        agent_name VARCHAR(100) NOT NULL,
        status ENUM('success', 'failed') NOT NULL,
        result JSON,
        execution_time DECIMAL(10,3),
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_agent_name (agent_name),
        INDEX idx_executed_at (executed_at),
        INDEX idx_status (status)
    )
    """
    
    # Agent配置表
    config_sql = """
    CREATE TABLE IF NOT EXISTS agent_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        agent_name VARCHAR(100) NOT NULL UNIQUE,
        config_data JSON NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_agent_name (agent_name)
    )
    """
    
    # 用户推荐表
    recommendations_sql = """
    CREATE TABLE IF NOT EXISTS user_recommendations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        material_id INT NOT NULL,
        reason TEXT,
        score DECIMAL(3,2) DEFAULT 0.5,
        type VARCHAR(50) DEFAULT 'general',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at),
        INDEX idx_score (score)
    )
    """
    
    try:
        print("开始创建Agent相关表...")
        
        # 创建表
        db.execute_update(notifications_sql)
        print("✅ notifications表创建成功")
        
        db.execute_update(execution_logs_sql)
        print("✅ agent_execution_logs表创建成功")
        
        db.execute_update(config_sql)
        print("✅ agent_config表创建成功")
        
        db.execute_update(recommendations_sql)
        print("✅ user_recommendations表创建成功")
        
        # 插入默认配置
        insert_default_config()
        
        print("\n🎉 Agent表初始化完成！")
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")

def insert_default_config():
    """插入默认Agent配置"""
    try:
        default_configs = [
            {
                'agent_name': 'InventoryAlert',
                'config_data': {
                    'low_stock_threshold': 10,
                    'schedule_time': '08:00',
                    'interval': 'daily',
                    'enabled': True
                }
            },
            {
                'agent_name': 'SmartRecommendation',
                'config_data': {
                    'max_recommendations': 5,
                    'similarity_threshold': 0.1,
                    'schedule_time': '22:00',
                    'interval': 'daily',
                    'enabled': True
                }
            },
            {
                'agent_name': 'DataAnalysis',
                'config_data': {
                    'analysis_period_months': 3,
                    'schedule_time': 'monday.09:00',
                    'interval': 'weekly',
                    'enabled': True
                }
            }
        ]
        
        for config in default_configs:
            # 检查是否已存在
            check_sql = "SELECT id FROM agent_config WHERE agent_name = %s"
            existing = db.execute_query_one(check_sql, (config['agent_name'],))
            
            if not existing:
                insert_sql = """
                INSERT INTO agent_config (agent_name, config_data)
                VALUES (%s, %s)
                """
                import json
                db.execute_insert(insert_sql, (
                    config['agent_name'],
                    json.dumps(config['config_data'], ensure_ascii=False)
                ))
                print(f"✅ {config['agent_name']} 默认配置已插入")
            else:
                print(f"⚠️  {config['agent_name']} 配置已存在，跳过")
                
    except Exception as e:
        print(f"❌ 插入默认配置失败: {e}")

def create_sample_notifications():
    """创建示例通知"""
    try:
        # 获取管理员用户
        admin_sql = "SELECT id FROM users WHERE role = 'admin' LIMIT 1"
        admin = db.execute_query_one(admin_sql)
        
        if admin:
            sample_notifications = [
                {
                    'title': '系统启动通知',
                    'message': 'Agent智能系统已成功启动，开始监控物资管理状态。',
                    'type': 'system'
                },
                {
                    'title': '库存预警测试',
                    'message': '这是一条测试通知，用于验证通知系统是否正常工作。',
                    'type': 'admin'
                }
            ]
            
            for notification in sample_notifications:
                insert_sql = """
                INSERT INTO notifications (user_id, title, message, type, is_read)
                VALUES (%s, %s, %s, %s, %s)
                """
                db.execute_insert(insert_sql, (
                    admin['id'],
                    notification['title'],
                    notification['message'],
                    notification['type'],
                    False
                ))
            
            print(f"✅ 已创建 {len(sample_notifications)} 条示例通知")
        else:
            print("⚠️  未找到管理员用户，跳过创建示例通知")
            
    except Exception as e:
        print(f"❌ 创建示例通知失败: {e}")

if __name__ == '__main__':
    print("Agent数据表初始化工具")
    print("=" * 50)
    
    init_agent_tables()
    
    # 询问是否创建示例数据
    create_samples = input("\n是否创建示例通知数据？(y/n): ").lower().strip()
    if create_samples == 'y':
        create_sample_notifications()
    
    print("\n初始化完成！")
