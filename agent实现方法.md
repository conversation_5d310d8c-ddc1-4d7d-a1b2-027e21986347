# Agent系统部署和使用指南

## 1. 系统架构概览

```
物资管理系统 + Agent智能模块
├── 核心Agent
│   ├── 库存预警Agent (InventoryAlertAgent)
│   ├── 智能推荐Agent (SmartRecommendationAgent)  
│   └── 数据分析Agent (DataAnalysisAgent)
├── Agent调度器 (AgentScheduler)
├── 通知系统 (NotificationService)
└── 监控面板 (Agent Dashboard)
```

## 2. 部署步骤

### 2.1 安装依赖包

```bash
# 在原有的requirements.txt基础上添加以下依赖
pip install schedule==1.2.0
pip install pandas==2.0.3
pip install scikit-learn==1.3.0
pip install numpy==1.24.3
```

### 2.2 数据库更新

执行以下SQL脚本创建Agent相关表：

```sql
-- 执行database_schema_update.sql中的内容
-- 创建agent_execution_logs, notifications, agent_config, user_recommendations表
```

### 2.3 文件结构更新

```
金融系统/
├── agents/                  # 新增Agent模块
│   ├── __init__.py
│   ├── base_agent.py
│   ├── inventory_alert_agent.py
│   ├── smart_recommendation_agent.py
│   ├── data_analysis_agent.py
│   └── agent_scheduler.py
├── controllers/
│   ├── agent_controller.py  # 新增
│   └── api_controller.py    # 新增
├── services/
│   ├── agent_service.py     # 新增
│   ├── notification_service.py  # 新增
│   └── recommendation_service.py  # 新增
├── templates/
│   └── agent/               # 新增
│       ├── dashboard.html
│       ├── notifications.html
│       └── config.html
```

### 2.4 修改主应用文件

在`app.py`中添加Agent初始化：

```python
from agents.agent_scheduler import AgentScheduler
from agents.inventory_alert_agent import InventoryAlertAgent
from agents.smart_recommendation_agent import SmartRecommendationAgent
from agents.data_analysis_agent import DataAnalysisAgent

# 初始化Agent系统
def init_agents(app):
    scheduler = AgentScheduler()
    
    # 注册Agent
    inventory_agent = InventoryAlertAgent()
    scheduler.register_agent(inventory_agent, "08:00", "daily")
    
    recommendation_agent = SmartRecommendationAgent()
    scheduler.register_agent(recommendation_agent, "22:00", "daily")
    
    analysis_agent = DataAnalysisAgent()
    scheduler.register_agent(analysis_agent, "09:00", "weekly")
    
    scheduler.start_scheduler()
    app.config['AGENT_SCHEDULER'] = scheduler
    return scheduler

# 在创建Flask应用后调用
app = Flask(__name__)
# ... 其他初始化代码 ...
scheduler = init_agents(app)

# 注册新的蓝图
from controllers.agent_controller import agent_bp
from controllers.api_controller import api_bp
app.register_blueprint(agent_bp)
app.register_blueprint(api_bp)
```

## 3. Agent功能详解

### 3.1 库存预警Agent

**功能**：
- 每天早上8点自动检查库存
- 检测低库存物资（数量≤10）
- 自动发送预警通知给管理员

**配置项**：
```json
{
  "low_stock_threshold": 10,
  "expiry_warning_days": 30,
  "schedule_time": "08:00"
}
```

### 3.2 智能推荐Agent

**功能**：
- 每天晚上22点分析用户行为
- 基于协同过滤算法推荐物资
- 分析用户申请历史和偏好
- 为活跃用户生成个性化推荐

**推荐算法**：
- 使用余弦相似度计算用户相似性
- 基于相似用户的申请历史推荐
- 过滤用户已申请过的物资

### 3.3 数据分析Agent

**功能**：
- 每周一早上9点执行数据分析
- 分析物资使用模式
- 进行成本效益分析
- 预测物资需求趋势
- 检测异常使用模式

## 4. 使用指南

### 4.1 管理员使用

**访问Agent监控面板**：
1. 以管理员身份登录系统
2. 访问 `/agent/dashboard`
3. 查看各Agent运行状态和执行历史

**手动执行Agent**：
1. 在监控面板点击"手动执行"
2. 确认执行后等待结果
3. 查看执行结果和生成的通知

**配置Agent参数**：
1. 点击Agent卡片中的"配置"
2. 修改相应参数（如预警阈值）
3. 保存配置，下次执行时生效

### 4.2 普通用户使用

**查看智能推荐**：
1. 在物资申请页面查看推荐区域
2. 系统会显示个性化推荐的物资
3. 点击"选择此物资"快速申请

**查看通知**：
1. 访问 `/agent/notifications`
2. 查看系统发送的各类通知
3. 标记重要通知为已读

## 5. 监控和维护

### 5.1 性能监控

**监控指标**：
- Agent执行频率和耗时
- 成功率和错误率
- 生成通知数量
- 推荐准确率

**监控方法**：
```python
# 查看Agent执行统计
SELECT agent_name, 
       COUNT(*) as total_executions,
       AVG(TIMESTAMPDIFF(SECOND, executed_at, NOW())) as avg_duration,
       SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) / COUNT(*) * 100 as success_rate
FROM agent_execution_logs 
WHERE executed_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY agent_name;
```

### 5.2 日志管理

**日志位置**：
- Agent执行日志存储在 `agent_execution_logs` 表
- 应用日志在 `logs/agent.log`

**日志清理**：
```sql
-- 清理30天前的执行日志
DELETE FROM agent_execution_logs 
WHERE executed_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 5.3 故障排除

**常见问题**：

1. **Agent执行失败**
   - 检查数据库连接
   - 查看错误日志
   - 验证依赖包安装

2. **推荐不准确**
   - 检查用户申请历史数据
   - 调整相似度阈值
   - 增加训练数据量

3. **通知发送失败**
   - 检查通知服务配置
   - 验证邮件/短信服务设置

## 6. 扩展和定制

### 6.1 添加新Agent

1. 继承 `BaseAgent` 类
2. 实现 `execute()` 方法
3. 在调度器中注册
4. 添加相应的配置和监控

```python
class CustomAgent(BaseAgent):
    def __init__(self):
        super().__init__("CustomAgent")
    
    def execute(self):
        # 实现具体逻辑
        return {"status": "success", "message": "执行完成"}
```

### 6.2 集成外部服务

**邮件通知**：
```python
import smtplib
from email.mime.text import MIMEText

class EmailNotificationService:
    def send_email(self, to_email, subject, content):
        # 实现邮件发送逻辑
        pass
```

**微信通知**：
```python
import requests

class WeChatNotificationService:
    def send_wechat_message(self, user_id, message):
        # 实现微信消息推送
        pass
```

### 6.3 机器学习增强

**需求预测模型**：
```python
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor

class DemandPredictionAgent(BaseAgent):
    def __init__(self):
        super().__init__("DemandPrediction")
        self.model = RandomForestRegressor()
    
    def train_model(self, historical_data):
        # 训练需求预测模型
        pass
    
    def predict_demand(self, features):
        # 预测未来需求
        return self.model.predict(features)
```

## 7. 安全考虑

### 7.1 权限控制
- Agent管理功能仅限管理员访问
- API接口需要身份验证
- 敏感数据加密存储

### 7.2 数据保护
- 用户推荐数据脱敏处理
- 定期备份Agent配置和日志
- 限制数据访问权限

### 7.3 系统稳定性
- Agent执行错误不影响主系统
- 设置执行超时机制
- 异常情况下自动重试

## 8. 性能优化

### 8.1 数据库优化
- 为频繁查询的字段添加索引
- 定期清理历史日志数据
- 使用连接池管理数据库连接

### 8.2 算法优化
- 推荐算法使用增量更新
- 大数据集分批处理
- 缓存常用计算结果

### 8.3 资源管理
- 控制Agent并发执行数量
- 监控内存和CPU使用
- 设置合理的执行间隔

通过以上的Agent系统，您的物资管理系统将具备智能化的预警、推荐和分析能力，大大提升系统的智能化水平和用户体验。