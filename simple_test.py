#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试修复后的功能
"""

from dao.database import Database
from services.agent_service import AgentService

def test_recommendations():
    """测试推荐功能"""
    print("=== 测试推荐功能 ===")
    
    try:
        agent_service = AgentService()
        
        # 测试admin用户的推荐
        recommendations = agent_service.get_user_recommendations(1, 5)
        print(f"admin用户推荐数量: {len(recommendations)}")
        
        for i, rec in enumerate(recommendations):
            print(f"推荐 {i+1}:")
            print(f"  物资: {rec.get('material_name', 'unknown')}")
            print(f"  类别: {rec.get('category', 'unknown')}")
            print(f"  评分: {rec.get('score', 0)}")
            print(f"  价格: ¥{rec.get('unit_price', 0)}")
            print(f"  库存: {rec.get('remaining_quantity', 0)}")
            print(f"  理由: {rec.get('reason', 'unknown')}")
            print()
            
        return len(recommendations) > 0
        
    except Exception as e:
        print(f"推荐功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_status():
    """测试Agent状态"""
    print("=== 测试Agent状态 ===")
    
    try:
        agent_service = AgentService()
        
        status = agent_service.get_agent_status()
        print(f"调度器运行状态: {status.get('scheduler_running', False)}")
        print(f"Agent总数: {status.get('total_agents', 0)}")
        
        agents = status.get('agents', {})
        for agent_name, agent_info in agents.items():
            print(f"  {agent_name}: {agent_info.get('status', 'unknown')}")
            
        return status.get('scheduler_running', False)
        
    except Exception as e:
        print(f"Agent状态测试失败: {e}")
        return False

def test_execution_history():
    """测试执行历史"""
    print("=== 测试执行历史 ===")
    
    try:
        agent_service = AgentService()
        
        history = agent_service.get_execution_history(limit=5)
        print(f"执行历史记录数量: {len(history)}")
        
        for i, record in enumerate(history):
            print(f"记录 {i+1}:")
            print(f"  Agent: {record.get('agent_name', 'unknown')}")
            print(f"  状态: {record.get('status', 'unknown')}")
            print(f"  执行时间: {record.get('executed_at', 'unknown')}")
            print(f"  耗时: {record.get('execution_time', 0)}秒")
            print()
            
        return len(history) > 0
        
    except Exception as e:
        print(f"执行历史测试失败: {e}")
        return False

def main():
    """主函数"""
    print("简单功能测试")
    print("=" * 50)
    
    results = []
    
    # 测试各项功能
    results.append(("推荐功能", test_recommendations()))
    results.append(("Agent状态", test_agent_status()))
    results.append(("执行历史", test_execution_history()))
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    print(f"\n总体结果: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")

if __name__ == '__main__':
    main()
