#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录功能
"""

import requests
import sys

def test_login():
    """测试登录功能"""
    base_url = 'http://localhost:5000'
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 获取登录页面
        print("1. 获取登录页面...")
        response = session.get(f'{base_url}/auth/login')
        if response.status_code == 200:
            print("✅ 登录页面加载成功")
        else:
            print(f"❌ 登录页面加载失败，状态码: {response.status_code}")
            return False
        
        # 2. 尝试登录
        print("2. 尝试管理员登录...")
        login_data = {
            'username': 'admin',
            'password': '123456'
        }
        
        response = session.post(f'{base_url}/auth/login', data=login_data, allow_redirects=False)
        
        if response.status_code == 302:  # 重定向表示登录成功
            print("✅ 管理员登录成功")
            redirect_url = response.headers.get('Location', '')
            print(f"   重定向到: {redirect_url}")
        else:
            print(f"❌ 登录失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            return False
        
        # 3. 访问仪表板
        print("3. 访问管理员仪表板...")
        response = session.get(f'{base_url}/')
        
        if response.status_code == 200:
            print("✅ 仪表板访问成功")
            if '物资管理系统' in response.text:
                print("✅ 页面内容正确")
            else:
                print("⚠️  页面内容可能有问题")
        else:
            print(f"❌ 仪表板访问失败，状态码: {response.status_code}")
            return False
        
        # 4. 测试普通用户登录
        print("4. 测试普通用户登录...")
        session.get(f'{base_url}/auth/logout')  # 先登出
        
        login_data = {
            'username': 'zhangsan',
            'password': '123456'
        }
        
        response = session.post(f'{base_url}/auth/login', data=login_data, allow_redirects=False)
        
        if response.status_code == 302:
            print("✅ 普通用户登录成功")
        else:
            print(f"❌ 普通用户登录失败，状态码: {response.status_code}")
            return False
        
        print("\n🎉 所有登录测试通过！")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

if __name__ == '__main__':
    print("物资管理系统登录功能测试")
    print("=" * 50)
    
    success = test_login()
    
    print("=" * 50)
    if success:
        print("✅ 系统功能正常！")
        print("   访问地址: http://localhost:5000")
        print("   管理员: admin / 123456")
        print("   普通用户: zhangsan / 123456")
    else:
        print("❌ 系统存在问题")
        sys.exit(1)
