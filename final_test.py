#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试所有修复的功能
"""

import time
import requests
import json

BASE_URL = 'http://localhost:5000'

def wait_for_server():
    """等待服务器启动"""
    print("等待服务器启动...")
    for i in range(10):
        try:
            response = requests.get(f'{BASE_URL}/', timeout=2)
            if response.status_code == 200:
                print("✅ 服务器已启动")
                return True
        except:
            pass
        time.sleep(1)
    return False

def login_as_admin():
    """以管理员身份登录"""
    session = requests.Session()
    
    try:
        # 获取登录页面
        response = session.get(f'{BASE_URL}/auth/login', timeout=5)
        
        # 登录
        login_data = {'username': 'admin', 'password': 'admin'}
        response = session.post(f'{BASE_URL}/auth/login', data=login_data, timeout=5)
        
        if response.status_code == 200 and 'login' not in response.url:
            return session
        elif response.status_code == 302:
            return session
    except Exception as e:
        print(f"登录失败: {e}")
    
    return None

def test_recommendations_page(session):
    """测试推荐页面"""
    print("\n=== 测试推荐页面 ===")
    
    try:
        # 访问推荐页面
        response = session.get(f'{BASE_URL}/agent/recommendations', timeout=5)
        print(f"推荐页面状态: {response.status_code}")
        
        if response.status_code == 200:
            if '智能推荐' in response.text and '联想ThinkPad笔记本' in response.text:
                print("✅ 推荐页面显示正常，包含推荐内容")
                return True
            elif '暂无推荐' in response.text:
                print("❌ 推荐页面显示'暂无推荐'")
                return False
            else:
                print("✅ 推荐页面访问成功")
                return True
        else:
            print(f"❌ 推荐页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 推荐页面测试失败: {e}")
        return False

def test_recommendations_api(session):
    """测试推荐API"""
    print("\n=== 测试推荐API ===")
    
    try:
        response = session.get(f'{BASE_URL}/agent/api/recommendations', timeout=5)
        print(f"推荐API状态: {response.status_code}")
        
        if response.status_code == 200:
            recommendations = response.json()
            print(f"✅ 推荐数量: {len(recommendations)}")
            
            if len(recommendations) > 0:
                print("推荐内容:")
                for i, rec in enumerate(recommendations[:3]):
                    print(f"  {i+1}. {rec.get('material_name', 'unknown')} - 评分: {rec.get('score', 0)}")
                return True
            else:
                print("❌ 没有推荐内容")
                return False
        else:
            print(f"❌ 推荐API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 推荐API测试失败: {e}")
        return False

def test_agent_dashboard(session):
    """测试Agent监控面板"""
    print("\n=== 测试Agent监控面板 ===")
    
    try:
        # 访问监控面板
        response = session.get(f'{BASE_URL}/agent/dashboard', timeout=5)
        print(f"监控面板状态: {response.status_code}")
        
        if response.status_code == 200:
            if 'Agent智能监控面板' in response.text:
                print("✅ 监控面板访问成功")
                return True
            else:
                print("❌ 监控面板内容异常")
                return False
        else:
            print(f"❌ 监控面板访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 监控面板测试失败: {e}")
        return False

def test_agent_status_api(session):
    """测试Agent状态API"""
    print("\n=== 测试Agent状态API ===")
    
    try:
        response = session.get(f'{BASE_URL}/agent/api/status', timeout=5)
        print(f"状态API状态: {response.status_code}")
        
        if response.status_code == 200:
            status = response.json()
            print(f"✅ 调度器运行: {status.get('scheduler_running', False)}")
            print(f"✅ Agent数量: {status.get('total_agents', 0)}")
            
            agents = status.get('agents', {})
            for name, info in agents.items():
                print(f"  {name}: {info.get('status', 'unknown')}")
            
            return status.get('scheduler_running', False)
        else:
            print(f"❌ 状态API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 状态API测试失败: {e}")
        return False

def test_notifications(session):
    """测试通知功能"""
    print("\n=== 测试通知功能 ===")
    
    try:
        # 访问通知页面
        response = session.get(f'{BASE_URL}/agent/notifications', timeout=5)
        print(f"通知页面状态: {response.status_code}")
        
        if response.status_code == 200:
            if '系统通知' in response.text:
                print("✅ 通知页面访问成功")
                
                # 测试未读通知数量API
                response = session.get(f'{BASE_URL}/agent/api/notifications/unread_count', timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    count = data.get('count', 0)
                    print(f"✅ 未读通知数量: {count}")
                    return True
                else:
                    print("❌ 未读通知数量API失败")
                    return False
            else:
                print("❌ 通知页面内容异常")
                return False
        else:
            print(f"❌ 通知页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 通知功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("最终功能测试")
    print("=" * 60)
    
    # 等待服务器启动
    if not wait_for_server():
        print("❌ 服务器未启动，请先启动应用程序")
        return
    
    # 登录
    session = login_as_admin()
    if not session:
        print("❌ 无法登录")
        return
    
    print("✅ 登录成功")
    
    # 执行所有测试
    tests = [
        ("推荐页面", lambda: test_recommendations_page(session)),
        ("推荐API", lambda: test_recommendations_api(session)),
        ("Agent监控面板", lambda: test_agent_dashboard(session)),
        ("Agent状态API", lambda: test_agent_status_api(session)),
        ("通知功能", lambda: test_notifications(session)),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n通过率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("🎉 所有功能测试通过！")
    else:
        print("⚠️  部分功能需要进一步检查")

if __name__ == '__main__':
    main()
