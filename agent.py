# agents/base_agent.py
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Any


class BaseAgent(ABC):
    """Agent基类"""

    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"agent.{name}")
        self.is_active = True

    @abstractmethod
    def execute(self) -> Dict[str, Any]:
        """执行Agent任务"""
        pass

    def log_action(self, action: str, details: str = ""):
        """记录Agent操作日志"""
        self.logger.info(f"Agent {self.name}: {action} - {details}")


# agents/inventory_alert_agent.py
from dao.material_dao import MaterialDAO
from services.notification_service import NotificationService


class InventoryAlertAgent(BaseAgent):
    """库存预警Agent"""

    def __init__(self):
        super().__init__("InventoryAlert")
        self.material_dao = MaterialDAO()
        self.notification_service = NotificationService()
        self.low_stock_threshold = 10  # 低库存阈值

    def execute(self) -> Dict[str, Any]:
        """执行库存检查"""
        results = {
            'low_stock_items': [],
            'expired_items': [],
            'expiring_items': [],
            'notifications_sent': 0
        }

        try:
            # 检查低库存物资
            low_stock_items = self.check_low_stock()
            results['low_stock_items'] = low_stock_items

            # 检查过期物资
            expired_items = self.check_expired_materials()
            results['expired_items'] = expired_items

            # 检查即将过期物资
            expiring_items = self.check_expiring_materials()
            results['expiring_items'] = expiring_items

            # 发送通知
            notifications_sent = self.send_notifications(
                low_stock_items, expired_items, expiring_items
            )
            results['notifications_sent'] = notifications_sent

            self.log_action("库存检查完成",
                            f"低库存:{len(low_stock_items)}, "
                            f"已过期:{len(expired_items)}, "
                            f"即将过期:{len(expiring_items)}")

        except Exception as e:
            self.logger.error(f"库存检查失败: {str(e)}")
            results['error'] = str(e)

        return results

    def check_low_stock(self) -> List[Dict]:
        """检查低库存物资"""
        query = """
        SELECT m.*, d.name as department_name 
        FROM materials m 
        LEFT JOIN departments d ON m.department_id = d.id 
        WHERE m.quantity <= %s AND m.status = '可用'
        """
        return self.material_dao.execute_query(query, (self.low_stock_threshold,))

    def check_expired_materials(self) -> List[Dict]:
        """检查已过期物资"""
        today = datetime.now().date()
        query = """
        SELECT m.*, d.name as department_name 
        FROM materials m 
        LEFT JOIN departments d ON m.department_id = d.id 
        WHERE m.expiry_date < %s AND m.status != '报废'
        """
        return self.material_dao.execute_query(query, (today,))

    def check_expiring_materials(self, days_ahead: int = 30) -> List[Dict]:
        """检查即将过期物资"""
        today = datetime.now().date()
        future_date = today + timedelta(days=days_ahead)
        query = """
        SELECT m.*, d.name as department_name 
        FROM materials m 
        LEFT JOIN departments d ON m.department_id = d.id 
        WHERE m.expiry_date BETWEEN %s AND %s AND m.status != '报废'
        """
        return self.material_dao.execute_query(query, (today, future_date))

    def send_notifications(self, low_stock, expired, expiring) -> int:
        """发送通知"""
        notifications_sent = 0

        # 发送低库存通知
        if low_stock:
            message = f"发现{len(low_stock)}项物资库存不足，请及时补充"
            self.notification_service.send_admin_notification(
                "库存预警", message, low_stock
            )
            notifications_sent += 1

        # 发送过期物资通知
        if expired:
            message = f"发现{len(expired)}项物资已过期，请及时处理"
            self.notification_service.send_admin_notification(
                "过期物资提醒", message, expired
            )
            notifications_sent += 1

        # 发送即将过期通知
        if expiring:
            message = f"发现{len(expiring)}项物资即将过期，请注意使用"
            self.notification_service.send_admin_notification(
                "即将过期提醒", message, expiring
            )
            notifications_sent += 1

        return notifications_sent


# agents/smart_recommendation_agent.py
from dao.material_dao import MaterialDAO
from dao.allocation_dao import AllocationDAO
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity


class SmartRecommendationAgent(BaseAgent):
    """智能推荐Agent"""

    def __init__(self):
        super().__init__("SmartRecommendation")
        self.material_dao = MaterialDAO()
        self.allocation_dao = AllocationDAO()

    def execute(self) -> Dict[str, Any]:
        """执行推荐任务"""
        results = {
            'recommendations_generated': 0,
            'users_processed': 0
        }

        try:
            # 获取活跃用户
            active_users = self.get_active_users()
            results['users_processed'] = len(active_users)

            # 为每个用户生成推荐
            total_recommendations = 0
            for user in active_users:
                user_recommendations = self.generate_user_recommendations(user['id'])
                if user_recommendations:
                    self.save_recommendations(user['id'], user_recommendations)
                    total_recommendations += len(user_recommendations)

            results['recommendations_generated'] = total_recommendations

            self.log_action("推荐生成完成",
                            f"处理用户:{len(active_users)}, "
                            f"生成推荐:{total_recommendations}")

        except Exception as e:
            self.logger.error(f"推荐生成失败: {str(e)}")
            results['error'] = str(e)

        return results

    def get_active_users(self) -> List[Dict]:
        """获取活跃用户（最近30天有申请记录）"""
        query = """
        SELECT DISTINCT u.id, u.username, u.department_id
        FROM users u
        JOIN applications a ON u.id = a.user_id
        WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        """
        return self.material_dao.execute_query(query)

    def generate_user_recommendations(self, user_id: int) -> List[Dict]:
        """为用户生成物资推荐"""
        # 获取用户历史申请
        user_history = self.get_user_application_history(user_id)
        if not user_history:
            return []

        # 获取相似用户
        similar_users = self.find_similar_users(user_id)

        # 基于相似用户推荐物资
        recommendations = []
        for similar_user in similar_users[:5]:  # 取前5个相似用户
            similar_user_materials = self.get_user_recent_materials(similar_user['id'])
            for material in similar_user_materials:
                if not self.user_has_applied_material(user_id, material['id']):
                    recommendations.append({
                        'material_id': material['id'],
                        'material_name': material['name'],
                        'reason': f"与您相似的用户 {similar_user['username']} 也申请了此物资",
                        'similarity_score': similar_user['similarity_score']
                    })

        # 去重并按相似度排序
        unique_recommendations = {}
        for rec in recommendations:
            material_id = rec['material_id']
            if material_id not in unique_recommendations:
                unique_recommendations[material_id] = rec
            elif rec['similarity_score'] > unique_recommendations[material_id]['similarity_score']:
                unique_recommendations[material_id] = rec

        return list(unique_recommendations.values())[:10]  # 返回前10个推荐

    def find_similar_users(self, user_id: int) -> List[Dict]:
        """查找相似用户"""
        # 获取所有用户的申请历史
        all_users_history = self.get_all_users_history()

        # 构建用户-物资矩阵
        user_material_matrix = self.build_user_material_matrix(all_users_history)

        # 计算相似度
        if user_id not in user_material_matrix.index:
            return []

        target_user_vector = user_material_matrix.loc[user_id].values.reshape(1, -1)
        similarity_scores = cosine_similarity(target_user_vector, user_material_matrix.values)[0]

        # 排序并返回相似用户
        similar_users = []
        for i, score in enumerate(similarity_scores):
            other_user_id = user_material_matrix.index[i]
            if other_user_id != user_id and score > 0.1:  # 相似度阈值
                similar_users.append({
                    'id': other_user_id,
                    'username': self.get_username(other_user_id),
                    'similarity_score': score
                })

        return sorted(similar_users, key=lambda x: x['similarity_score'], reverse=True)


# agents/data_analysis_agent.py
import pandas as pd
import numpy as np
from datetime import datetime, timedelta


class DataAnalysisAgent(BaseAgent):
    """数据分析Agent"""

    def __init__(self):
        super().__init__("DataAnalysis")
        self.material_dao = MaterialDAO()
        self.allocation_dao = AllocationDAO()

    def execute(self) -> Dict[str, Any]:
        """执行数据分析"""
        results = {
            'usage_patterns': {},
            'cost_analysis': {},
            'trend_predictions': {},
            'anomalies': []
        }

        try:
            # 使用模式分析
            results['usage_patterns'] = self.analyze_usage_patterns()

            # 成本分析
            results['cost_analysis'] = self.analyze_costs()

            # 趋势预测
            results['trend_predictions'] = self.predict_trends()

            # 异常检测
            results['anomalies'] = self.detect_anomalies()

            self.log_action("数据分析完成", "生成使用模式、成本分析和趋势预测报告")

        except Exception as e:
            self.logger.error(f"数据分析失败: {str(e)}")
            results['error'] = str(e)

        return results

    def analyze_usage_patterns(self) -> Dict[str, Any]:
        """分析使用模式"""
        # 获取最近3个月的分配数据
        query = """
        SELECT m.category, m.name, a.quantity, a.allocated_at,
               d.name as department_name, u.username
        FROM allocations a
        JOIN materials m ON a.material_id = m.id
        JOIN departments d ON a.department_id = d.id
        JOIN users u ON a.user_id = u.id
        WHERE a.allocated_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
        """
        data = pd.DataFrame(self.allocation_dao.execute_query(query))

        if data.empty:
            return {}

        # 按类别统计使用量
        category_usage = data.groupby('category')['quantity'].sum().to_dict()

        # 按科室统计使用量
        department_usage = data.groupby('department_name')['quantity'].sum().to_dict()

        # 按时间统计使用趋势
        data['allocated_at'] = pd.to_datetime(data['allocated_at'])
        data['month'] = data['allocated_at'].dt.to_period('M')
        monthly_usage = data.groupby('month')['quantity'].sum().to_dict()

        return {
            'category_usage': category_usage,
            'department_usage': department_usage,
            'monthly_trend': {str(k): v for k, v in monthly_usage.items()}
        }

    def predict_trends(self) -> Dict[str, Any]:
        """趋势预测"""
        # 简单的线性趋势预测
        query = """
        SELECT DATE(allocated_at) as date, SUM(quantity) as daily_total
        FROM allocations
        WHERE allocated_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE(allocated_at)
        ORDER BY date
        """
        data = pd.DataFrame(self.allocation_dao.execute_query(query))

        if len(data) < 30:  # 数据不足
            return {}

        data['date'] = pd.to_datetime(data['date'])
        data = data.set_index('date').sort_index()

        # 简单移动平均预测下周的使用量
        recent_avg = data['daily_total'].tail(7).mean()
        trend = data['daily_total'].tail(14).mean() - data['daily_total'].head(14).mean()

        predicted_next_week = recent_avg + trend

        return {
            'next_week_prediction': float(predicted_next_week),
            'current_trend': 'increasing' if trend > 0 else 'decreasing',
            'trend_strength': abs(float(trend))
        }


# agents/agent_scheduler.py
import schedule
import time
import threading
from typing import List
import json


class AgentScheduler:
    """Agent调度器"""

    def __init__(self):
        self.agents = {}
        self.running = False
        self.scheduler_thread = None

    def register_agent(self, agent: BaseAgent, schedule_time: str, interval: str = 'daily'):
        """注册Agent"""
        self.agents[agent.name] = {
            'agent': agent,
            'schedule_time': schedule_time,
            'interval': interval
        }

        # 设置调度
        if interval == 'daily':
            schedule.every().day.at(schedule_time).do(self._run_agent, agent.name)
        elif interval == 'hourly':
            schedule.every().hour.at(f":{schedule_time}").do(self._run_agent, agent.name)
        elif interval == 'weekly':
            schedule.every().week.at(schedule_time).do(self._run_agent, agent.name)

    def _run_agent(self, agent_name: str):
        """运行Agent"""
        if agent_name in self.agents:
            agent = self.agents[agent_name]['agent']
            try:
                result = agent.execute()
                self._log_agent_result(agent_name, result)
            except Exception as e:
                self._log_agent_error(agent_name, str(e))

    def start_scheduler(self):
        """启动调度器"""
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler)
        self.scheduler_thread.daemon = True
        self.scheduler_thread.start()

    def stop_scheduler(self):
        """停止调度器"""
        self.running = False
        if self.scheduler_thread:
            self.scheduler_thread.join()

    def _run_scheduler(self):
        """运行调度循环"""
        while self.running:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次

    def _log_agent_result(self, agent_name: str, result: Dict[str, Any]):
        """记录Agent执行结果"""
        # 可以保存到数据库或日志文件
        print(f"Agent {agent_name} 执行完成: {json.dumps(result, ensure_ascii=False)}")

    def _log_agent_error(self, agent_name: str, error: str):
        """记录Agent执行错误"""
        print(f"Agent {agent_name} 执行失败: {error}")


# services/notification_service.py
class NotificationService:
    """通知服务"""

    def __init__(self):
        pass

    def send_admin_notification(self, title: str, message: str, data: List[Dict] = None):
        """发送管理员通知"""
        # 这里可以实现邮件发送、短信发送、系统内通知等
        # 暂时简单实现为保存到数据库
        notification_data = {
            'title': title,
            'message': message,
            'type': 'admin',
            'data': json.dumps(data) if data else None,
            'created_at': datetime.now(),
            'is_read': False
        }
        # 保存到notifications表
        pass

    def send_user_notification(self, user_id: int, title: str, message: str):
        """发送用户通知"""
        notification_data = {
            'user_id': user_id,
            'title': title,
            'message': message,
            'type': 'user',
            'created_at': datetime.now(),
            'is_read': False
        }
        # 保存到notifications表
        pass