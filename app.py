from flask import Flask, redirect, url_for, session
from config import Config
import logging
import os

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # 配置日志
    if not os.path.exists('logs'):
        os.makedirs('logs')

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s %(name)s: %(message)s',
        handlers=[
            logging.FileHandler('logs/agent.log'),
            logging.StreamHandler()
        ]
    )

    # 注册蓝图
    from controllers.auth_controller import auth_bp
    from controllers.material_controller import material_bp
    from controllers.report_controller import report_bp
    from controllers.agent_controller import agent_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(material_bp)
    app.register_blueprint(report_bp)
    app.register_blueprint(agent_bp)

    # 初始化Agent系统
    scheduler = init_agents(app)

    # 根路由
    @app.route('/')
    def index():
        if 'user_id' in session:
            return redirect(url_for('material.dashboard'))
        return redirect(url_for('auth.login'))

    return app

def init_agents(app):
    """初始化Agent系统"""
    try:
        from agents.agent_scheduler import AgentScheduler
        from agents.inventory_alert_agent import InventoryAlertAgent
        from agents.data_analysis_agent import DataAnalysisAgent
        from services.agent_service import AgentService

        # 创建调度器
        scheduler = AgentScheduler()

        # 注册Agent - 只保留库存预警和数据分析Agent
        inventory_agent = InventoryAlertAgent()
        scheduler.register_agent(inventory_agent, "08:00", "daily")

        analysis_agent = DataAnalysisAgent()
        scheduler.register_agent(analysis_agent, "monday.09:00", "weekly")

        # 启动调度器
        scheduler.start_scheduler()

        # 保存调度器引用
        app.config['AGENT_SCHEDULER'] = scheduler

        # 设置Agent服务的调度器引用
        agent_service = AgentService()
        agent_service.set_scheduler(scheduler)
        app.config['AGENT_SERVICE'] = agent_service

        print("Agent系统初始化成功")
        return scheduler

    except Exception as e:
        print(f"Agent系统初始化失败: {e}")
        return None

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
