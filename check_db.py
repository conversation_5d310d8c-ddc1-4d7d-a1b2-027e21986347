#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

try:
    conn = pymysql.connect(
        host='localhost',
        user='root',
        password='123456',
        database='goods'
    )
    cursor = conn.cursor()
    
    print("检查科室表:")
    cursor.execute('SELECT id, name FROM departments')
    departments = cursor.fetchall()
    for row in departments:
        print(f'ID: {row[0]}, 名称: {row[1]}')
    
    print(f"\n科室总数: {len(departments)}")
    
    print("\n检查用户表:")
    cursor.execute('SELECT id, username, department_id FROM users')
    users = cursor.fetchall()
    for row in users:
        print(f'ID: {row[0]}, 用户名: {row[1]}, 科室ID: {row[2]}')
    
    print(f"\n用户总数: {len(users)}")

    print("\n检查物资表:")
    cursor.execute('SELECT COUNT(*) FROM materials')
    material_count = cursor.fetchone()[0]
    print(f"物资总数: {material_count}")

    if material_count > 0:
        cursor.execute('SELECT id, name, category, status FROM materials LIMIT 10')
        print("\n前10个物资:")
        for row in cursor.fetchall():
            print(f'ID: {row[0]}, 名称: {row[1]}, 类别: {row[2]}, 状态: {row[3]}')

    print("\n检查分配记录表:")
    cursor.execute('SELECT COUNT(*) FROM material_allocations')
    allocation_count = cursor.fetchone()[0]
    print(f"分配记录总数: {allocation_count}")

    if allocation_count > 0:
        cursor.execute('''
            SELECT ma.id, m.name, d.name, u.real_name, ma.quantity, ma.allocation_date, ma.status
            FROM material_allocations ma
            JOIN materials m ON ma.material_id = m.id
            JOIN departments d ON ma.department_id = d.id
            JOIN users u ON ma.user_id = u.id
            LIMIT 5
        ''')
        print("\n前5条分配记录:")
        for row in cursor.fetchall():
            print(f"ID: {row[0]}, 物资: {row[1]}, 科室: {row[2]}, 用户: {row[3]}, 数量: {row[4]}, 日期: {row[5]}, 状态: {row[6]}")

    print("\n检查申请记录表:")
    cursor.execute('SELECT COUNT(*) FROM material_requests')
    request_count = cursor.fetchone()[0]
    print(f"申请记录总数: {request_count}")

    if request_count > 0:
        cursor.execute('''
            SELECT mr.id, m.name, u.real_name, mr.quantity, mr.reason, mr.status, mr.request_date
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            JOIN users u ON mr.user_id = u.id
            WHERE mr.status = 'pending'
            ORDER BY mr.request_date DESC
        ''')
        print("\n待审核申请:")
        for row in cursor.fetchall():
            print(f"ID: {row[0]}, 物资: {row[1]}, 申请人: {row[2]}, 数量: {row[3]}, 原因: {row[4]}, 状态: {row[5]}, 日期: {row[6]}")

    conn.close()
    
except Exception as e:
    print(f"错误: {e}")
