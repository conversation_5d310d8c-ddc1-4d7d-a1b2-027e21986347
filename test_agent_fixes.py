#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Agent修复功能
"""

import time
from agents.data_analysis_agent import DataAnalysisAgent
from services.agent_service import AgentService

def test_data_analysis_agent():
    """测试数据分析Agent修复"""
    print("=== 测试数据分析Agent修复 ===")
    
    try:
        agent = DataAnalysisAgent()
        
        print("执行数据分析Agent...")
        start_time = time.time()
        result = agent.execute()
        execution_time = time.time() - start_time
        
        print(f"✅ DataAnalysis Agent执行成功")
        print(f"执行时间: {execution_time:.2f}秒")
        
        # 检查结果结构
        expected_keys = ['usage_patterns', 'cost_analysis', 'trend_analysis', 'anomalies', 'status']
        for key in expected_keys:
            if key in result:
                print(f"✅ 包含 {key} 数据")
            else:
                print(f"❌ 缺少 {key} 数据")
        
        # 检查成本分析是否有数据
        cost_analysis = result.get('cost_analysis', {})
        if cost_analysis:
            print(f"✅ 成本分析数据正常，包含 {len(cost_analysis)} 个分析维度")
        else:
            print("⚠️ 成本分析数据为空")
        
        return True
        
    except Exception as e:
        print(f"❌ DataAnalysis Agent执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_data_service():
    """测试分析数据服务"""
    print("\n=== 测试分析数据服务 ===")
    
    try:
        service = AgentService()
        
        print("获取分析数据...")
        analysis_data = service.get_analysis_data()
        
        print(f"✅ 分析数据获取成功")
        
        # 检查基础统计数据
        basic_stats = ['total_materials', 'total_requests', 'low_stock_count', 'total_value']
        for stat in basic_stats:
            value = analysis_data.get(stat, 0)
            print(f"  {stat}: {value}")
        
        # 检查图表数据
        chart_data = ['category_distribution', 'monthly_trend', 'popular_materials', 'low_stock_materials', 'user_activity']
        for data_type in chart_data:
            data = analysis_data.get(data_type, [])
            print(f"  {data_type}: {len(data)} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析数据服务失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_serialization():
    """测试JSON序列化修复"""
    print("\n=== 测试JSON序列化修复 ===")
    
    try:
        from agents.data_analysis_agent import convert_decimal
        from decimal import Decimal
        from datetime import datetime, date
        import json
        
        # 测试数据
        test_data = {
            'decimal_value': Decimal('123.45'),
            'datetime_value': datetime.now(),
            'date_value': date.today(),
            'nested': {
                'decimal_list': [Decimal('1.1'), Decimal('2.2')],
                'mixed': {
                    'price': Decimal('99.99'),
                    'date': date.today()
                }
            }
        }
        
        print("原始数据类型:")
        print(f"  decimal_value: {type(test_data['decimal_value'])}")
        print(f"  datetime_value: {type(test_data['datetime_value'])}")
        print(f"  date_value: {type(test_data['date_value'])}")
        
        # 转换数据
        converted_data = convert_decimal(test_data)
        
        print("转换后数据类型:")
        print(f"  decimal_value: {type(converted_data['decimal_value'])}")
        print(f"  datetime_value: {type(converted_data['datetime_value'])}")
        print(f"  date_value: {type(converted_data['date_value'])}")
        
        # 测试JSON序列化
        json_str = json.dumps(converted_data, indent=2)
        print("✅ JSON序列化成功")
        
        # 测试反序列化
        parsed_data = json.loads(json_str)
        print("✅ JSON反序列化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON序列化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("Agent修复功能测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试数据分析Agent
    if test_data_analysis_agent():
        success_count += 1
    
    # 测试分析数据服务
    if test_analysis_data_service():
        success_count += 1
    
    # 测试JSON序列化
    if test_json_serialization():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！Agent修复成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

if __name__ == '__main__':
    main()
