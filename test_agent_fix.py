#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Agent修复后的功能
"""

import requests
import json
import time

BASE_URL = 'http://localhost:5000'

def login_as_admin():
    """以管理员身份登录"""
    session = requests.Session()
    
    # 获取登录页面
    response = session.get(f'{BASE_URL}/auth/login')
    print(f"获取登录页面: {response.status_code}")
    
    # 尝试不同的管理员账户
    admin_accounts = [
        {'username': 'admin', 'password': 'admin123'},
        {'username': 'admin', 'password': '123456'},
        {'username': 'admin', 'password': 'admin'},
        {'username': 'root', 'password': 'root123'},
        {'username': 'administrator', 'password': 'admin123'}
    ]
    
    for account in admin_accounts:
        print(f"尝试登录: {account['username']}")
        response = session.post(f'{BASE_URL}/auth/login', data=account)
        
        if response.status_code == 200 and 'login' not in response.url:
            print(f"✅ 登录成功: {account['username']}")
            return session
        elif response.status_code == 302:  # 重定向
            print(f"✅ 登录成功 (重定向): {account['username']}")
            return session
    
    print("❌ 所有登录尝试都失败了")
    return None

def test_agent_status_api(session):
    """测试Agent状态API"""
    print("\n" + "="*50)
    print("测试Agent状态API")
    print("="*50)
    
    response = session.get(f'{BASE_URL}/agent/api/status')
    print(f"Agent状态API: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print("✅ Agent状态API正常")
            print(f"调度器运行状态: {data.get('scheduler_running', 'unknown')}")
            print(f"Agent数量: {data.get('total_agents', 0)}")
            
            agents = data.get('agents', {})
            for agent_name, status in agents.items():
                print(f"  - {agent_name}: {status.get('status', 'unknown')}")
                
            return True
        except json.JSONDecodeError:
            print("❌ 响应不是有效的JSON")
            print(f"响应内容: {response.text[:200]}")
            return False
    else:
        print(f"❌ Agent状态API失败: {response.status_code}")
        print(f"响应内容: {response.text[:200]}")
        return False

def test_manual_run_agent(session, agent_name):
    """测试手动运行Agent"""
    print(f"\n测试手动运行Agent: {agent_name}")
    print("-" * 30)
    
    response = session.post(f'{BASE_URL}/agent/api/run/{agent_name}')
    print(f"手动运行{agent_name}: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"✅ {agent_name}执行请求成功")
            if 'message' in data:
                print(f"消息: {data['message']}")
            if 'error' in data:
                print(f"错误: {data['error']}")
            return True
        except json.JSONDecodeError:
            print("❌ 响应不是有效的JSON")
            print(f"响应内容: {response.text[:200]}")
            return False
    else:
        print(f"❌ {agent_name}执行失败: {response.status_code}")
        print(f"响应内容: {response.text[:200]}")
        return False

def test_recommendations_page(session):
    """测试推荐页面"""
    print("\n" + "="*50)
    print("测试智能推荐页面")
    print("="*50)
    
    response = session.get(f'{BASE_URL}/agent/recommendations')
    print(f"访问推荐页面: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ 推荐页面访问成功")
        if '智能推荐' in response.text:
            print("✅ 页面内容正确")
            return True
        else:
            print("❌ 页面内容异常")
            return False
    else:
        print(f"❌ 推荐页面访问失败: {response.status_code}")
        return False

def test_recommendations_api(session):
    """测试推荐API"""
    print("\n测试推荐API")
    print("-" * 20)
    
    response = session.get(f'{BASE_URL}/agent/api/recommendations')
    print(f"推荐API: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"✅ 推荐API正常")
            print(f"推荐数量: {len(data)}")
            
            for i, rec in enumerate(data[:3]):  # 显示前3个推荐
                print(f"  推荐{i+1}: {rec.get('material_name', 'unknown')} - {rec.get('reason', 'no reason')}")
            
            return True
        except json.JSONDecodeError:
            print("❌ 响应不是有效的JSON")
            return False
    else:
        print(f"❌ 推荐API失败: {response.status_code}")
        return False

def main():
    """主函数"""
    print("Agent修复测试工具")
    print("=" * 50)
    
    # 登录
    session = login_as_admin()
    if not session:
        print("无法登录，测试终止")
        return
    
    # 测试Agent状态API
    status_ok = test_agent_status_api(session)
    
    if status_ok:
        # 测试手动运行Agent
        print("\n" + "="*50)
        print("测试手动运行Agent")
        print("="*50)
        
        agents = ['InventoryAlert', 'SmartRecommendation', 'DataAnalysis']
        for agent in agents:
            test_manual_run_agent(session, agent)
            time.sleep(2)  # 等待2秒
    
    # 测试推荐功能
    test_recommendations_page(session)
    test_recommendations_api(session)
    
    print("\n" + "="*50)
    print("测试完成")
    print("="*50)

if __name__ == '__main__':
    main()
