#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Agent执行功能
"""

import time
from agents.smart_recommendation_agent import SmartRecommendationAgent
from agents.inventory_alert_agent import InventoryAlertAgent
from agents.data_analysis_agent import DataAnalysisAgent
from dao.database import Database

def test_agent_execution():
    """测试Agent执行"""
    print("=== 测试Agent执行功能 ===")
    
    agents = [
        ("SmartRecommendation", SmartRecommendationAgent()),
        ("InventoryAlert", InventoryAlertAgent()),
        ("DataAnalysis", DataAnalysisAgent())
    ]
    
    for agent_name, agent in agents:
        print(f"\n--- 测试 {agent_name} Agent ---")
        
        try:
            start_time = time.time()
            result = agent.execute()
            execution_time = time.time() - start_time
            
            print(f"✅ {agent_name} 执行成功")
            print(f"执行时间: {execution_time:.2f}秒")
            print(f"执行结果: {result}")
            
        except Exception as e:
            print(f"❌ {agent_name} 执行失败: {e}")
            import traceback
            traceback.print_exc()

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    
    try:
        db = Database()
        
        # 测试查询
        sql = "SELECT COUNT(*) as count FROM users"
        result = db.execute_query(sql)
        print(f"✅ 数据库连接正常，用户数量: {result[0]['count']}")
        
        # 测试物资表
        sql = "SELECT COUNT(*) as count FROM materials"
        result = db.execute_query(sql)
        print(f"✅ 物资数量: {result[0]['count']}")
        
        # 测试申请表
        sql = "SELECT COUNT(*) as count FROM material_requests"
        result = db.execute_query(sql)
        print(f"✅ 申请记录数量: {result[0]['count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_agent_scheduler():
    """测试Agent调度器"""
    print("\n=== 测试Agent调度器 ===")
    
    try:
        from agents.agent_scheduler import AgentScheduler
        
        scheduler = AgentScheduler()
        
        # 注册Agent
        agents_config = [
            (SmartRecommendationAgent(), "09:00", "daily"),
            (InventoryAlertAgent(), "08:00", "daily"),
            (DataAnalysisAgent(), "10:00", "weekly")
        ]

        for agent, schedule_time, interval in agents_config:
            scheduler.register_agent(agent, schedule_time, interval)
        
        print("✅ Agent注册成功")
        
        # 获取状态
        status = scheduler.get_agent_status()
        print(f"调度器状态: {status}")
        
        # 测试手动执行
        print("\n测试手动执行SmartRecommendation Agent:")
        result = scheduler.manual_run_agent('SmartRecommendation')
        print(f"执行结果: {result}")
        
        # 等待一段时间让Agent执行完成
        time.sleep(3)
        
        # 再次获取状态
        status = scheduler.get_agent_status()
        print(f"执行后状态: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调度器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_data():
    """测试分析数据获取"""
    print("\n=== 测试分析数据获取 ===")
    
    try:
        from services.agent_service import AgentService
        
        service = AgentService()
        analysis_data = service.get_analysis_data()
        
        print("✅ 分析数据获取成功")
        print(f"物资总数: {analysis_data.get('total_materials', 0)}")
        print(f"申请总数: {analysis_data.get('total_requests', 0)}")
        print(f"低库存物资: {analysis_data.get('low_stock_count', 0)}")
        print(f"物资总价值: ¥{analysis_data.get('total_value', 0)}")
        
        # 显示类别分布
        category_dist = analysis_data.get('category_distribution', [])
        if category_dist:
            print("物资类别分布:")
            for item in category_dist:
                print(f"  {item['category']}: {item['count']}")
        
        # 显示热门物资
        popular = analysis_data.get('popular_materials', [])
        if popular:
            print("热门物资:")
            for item in popular[:5]:
                print(f"  {item['name']}: {item['request_count']}次申请")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析数据获取失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("Agent执行功能测试")
    print("=" * 60)
    
    # 测试数据库连接
    if not test_database_connection():
        print("❌ 数据库连接失败，停止测试")
        return
    
    # 测试Agent执行
    test_agent_execution()
    
    # 测试调度器
    test_agent_scheduler()
    
    # 测试分析数据
    test_analysis_data()
    
    print("\n" + "=" * 60)
    print("测试完成")

if __name__ == '__main__':
    main()
