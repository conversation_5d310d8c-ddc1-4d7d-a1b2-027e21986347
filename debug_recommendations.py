#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试推荐系统问题
"""

from dao.database import Database

def check_recommendations():
    """检查推荐数据"""
    db = Database()
    
    print("=== 检查推荐数据 ===")
    
    # 检查用户推荐表
    print("\n1. 用户推荐表数据:")
    try:
        sql = "SELECT * FROM user_recommendations ORDER BY created_at DESC LIMIT 10"
        recommendations = db.execute_query(sql)
        print(f"推荐记录数量: {len(recommendations)}")
        
        for rec in recommendations:
            print(f"  用户ID: {rec['user_id']}, 物资ID: {rec['material_id']}, 评分: {rec['score']}, 类型: {rec['type']}")
    except Exception as e:
        print(f"查询推荐表失败: {e}")
    
    # 检查用户表
    print("\n2. 用户表数据:")
    try:
        sql = "SELECT id, username, department FROM users"
        users = db.execute_query(sql)
        print(f"用户数量: {len(users)}")
        
        for user in users:
            print(f"  用户ID: {user['id']}, 用户名: {user['username']}, 部门: {user['department']}")
    except Exception as e:
        print(f"查询用户表失败: {e}")
    
    # 检查物资表
    print("\n3. 物资表数据:")
    try:
        sql = "SELECT id, name, category, remaining_quantity FROM materials WHERE status = 'available' LIMIT 10"
        materials = db.execute_query(sql)
        print(f"可用物资数量: {len(materials)}")
        
        for material in materials:
            print(f"  物资ID: {material['id']}, 名称: {material['name']}, 类别: {material['category']}, 库存: {material['remaining_quantity']}")
    except Exception as e:
        print(f"查询物资表失败: {e}")
    
    # 检查申请记录
    print("\n4. 申请记录:")
    try:
        sql = "SELECT user_id, material_id, quantity, status FROM material_requests ORDER BY created_at DESC LIMIT 10"
        requests = db.execute_query(sql)
        print(f"申请记录数量: {len(requests)}")
        
        for req in requests:
            print(f"  用户ID: {req['user_id']}, 物资ID: {req['material_id']}, 数量: {req['quantity']}, 状态: {req['status']}")
    except Exception as e:
        print(f"查询申请记录失败: {e}")

def test_recommendation_generation():
    """测试推荐生成"""
    print("\n=== 测试推荐生成 ===")
    
    try:
        from agents.smart_recommendation_agent import SmartRecommendationAgent
        
        agent = SmartRecommendationAgent()
        result = agent.execute()
        
        print(f"推荐生成结果: {result}")
        
        # 再次检查推荐数据
        check_recommendations()
        
    except Exception as e:
        print(f"推荐生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_recommendations()
    test_recommendation_generation()
