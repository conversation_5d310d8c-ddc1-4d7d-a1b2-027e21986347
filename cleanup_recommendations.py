#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理推荐相关数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dao.material_dao import MaterialDAO

def cleanup_recommendations():
    """清理推荐相关数据"""
    print("=== 清理推荐相关数据 ===")

    material_dao = MaterialDAO()
    db = material_dao.db
    
    try:
        # 检查推荐表是否存在
        check_sql = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'user_recommendations'
        """
        result = db.execute_query(check_sql)
        
        if result and result[0]['count'] > 0:
            # 检查推荐数据数量
            count_sql = "SELECT COUNT(*) as count FROM user_recommendations"
            count_result = db.execute_query(count_sql)
            recommendation_count = count_result[0]['count'] if count_result else 0
            
            print(f"发现 {recommendation_count} 条推荐记录")
            
            if recommendation_count > 0:
                # 删除所有推荐数据
                delete_sql = "DELETE FROM user_recommendations"
                db.execute_update(delete_sql)
                print(f"✅ 已删除 {recommendation_count} 条推荐记录")
            
            # 删除推荐表
            drop_sql = "DROP TABLE IF EXISTS user_recommendations"
            db.execute_update(drop_sql)
            print("✅ 已删除 user_recommendations 表")
        else:
            print("ℹ️ user_recommendations 表不存在，无需清理")
        
        # 清理Agent执行历史中的推荐Agent记录
        print("\n=== 清理Agent执行历史 ===")
        
        # 检查agent_executions表是否存在
        check_executions_sql = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'agent_executions'
        """
        executions_result = db.execute_query(check_executions_sql)
        
        if executions_result and executions_result[0]['count'] > 0:
            # 删除SmartRecommendation相关的执行记录
            delete_executions_sql = "DELETE FROM agent_executions WHERE agent_name = 'SmartRecommendation'"
            affected_rows = db.execute_update(delete_executions_sql)
            print(f"✅ 已删除 {affected_rows} 条SmartRecommendation执行记录")
        else:
            print("ℹ️ agent_executions 表不存在，无需清理")
        
        # 清理通知中的推荐相关通知
        print("\n=== 清理推荐相关通知 ===")
        
        # 检查notifications表是否存在
        check_notifications_sql = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'notifications'
        """
        notifications_result = db.execute_query(check_notifications_sql)
        
        if notifications_result and notifications_result[0]['count'] > 0:
            # 删除推荐相关的通知
            delete_notifications_sql = """
            DELETE FROM notifications 
            WHERE type = 'recommendation' 
            OR title LIKE '%推荐%' 
            OR message LIKE '%推荐%'
            """
            affected_notifications = db.execute_update(delete_notifications_sql)
            print(f"✅ 已删除 {affected_notifications} 条推荐相关通知")
        else:
            print("ℹ️ notifications 表不存在，无需清理")
        
        print("\n🎉 推荐相关数据清理完成！")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        import traceback
        traceback.print_exc()

def verify_cleanup():
    """验证清理结果"""
    print("\n=== 验证清理结果 ===")

    material_dao = MaterialDAO()
    db = material_dao.db
    
    try:
        # 检查推荐表是否还存在
        check_sql = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'user_recommendations'
        """
        result = db.execute_query(check_sql)
        
        if result and result[0]['count'] == 0:
            print("✅ user_recommendations 表已成功删除")
        else:
            print("⚠️ user_recommendations 表仍然存在")
        
        # 检查Agent执行记录
        check_executions_sql = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'agent_executions'
        """
        executions_result = db.execute_query(check_executions_sql)
        
        if executions_result and executions_result[0]['count'] > 0:
            count_sql = "SELECT COUNT(*) as count FROM agent_executions WHERE agent_name = 'SmartRecommendation'"
            count_result = db.execute_query(count_sql)
            remaining_count = count_result[0]['count'] if count_result else 0
            
            if remaining_count == 0:
                print("✅ SmartRecommendation执行记录已清理完成")
            else:
                print(f"⚠️ 仍有 {remaining_count} 条SmartRecommendation执行记录")
        
        # 检查推荐相关通知
        check_notifications_sql = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'notifications'
        """
        notifications_result = db.execute_query(check_notifications_sql)
        
        if notifications_result and notifications_result[0]['count'] > 0:
            count_sql = """
            SELECT COUNT(*) as count FROM notifications 
            WHERE type = 'recommendation' 
            OR title LIKE '%推荐%' 
            OR message LIKE '%推荐%'
            """
            count_result = db.execute_query(count_sql)
            remaining_notifications = count_result[0]['count'] if count_result else 0
            
            if remaining_notifications == 0:
                print("✅ 推荐相关通知已清理完成")
            else:
                print(f"⚠️ 仍有 {remaining_notifications} 条推荐相关通知")
        
        print("\n✅ 验证完成")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    cleanup_recommendations()
    verify_cleanup()
