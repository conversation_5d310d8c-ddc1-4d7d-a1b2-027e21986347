#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的功能
"""

import requests
import json

BASE_URL = 'http://localhost:5000'

def login_as_admin():
    """以管理员身份登录"""
    session = requests.Session()
    
    # 获取登录页面
    response = session.get(f'{BASE_URL}/auth/login')
    
    # 登录
    login_data = {'username': 'admin', 'password': 'admin'}
    response = session.post(f'{BASE_URL}/auth/login', data=login_data)
    
    if response.status_code == 200 and 'login' not in response.url:
        return session
    elif response.status_code == 302:
        return session
    return None

def test_recommendations(session):
    """测试推荐功能"""
    print("=== 测试推荐功能 ===")
    
    # 测试推荐API
    response = session.get(f'{BASE_URL}/agent/api/recommendations')
    print(f"推荐API状态: {response.status_code}")
    
    if response.status_code == 200:
        try:
            recommendations = response.json()
            print(f"✅ 推荐数量: {len(recommendations)}")
            
            for i, rec in enumerate(recommendations):
                print(f"推荐 {i+1}:")
                print(f"  物资: {rec.get('material_name', 'unknown')}")
                print(f"  类别: {rec.get('category', 'unknown')}")
                print(f"  评分: {rec.get('score', 0)}")
                print(f"  价格: ¥{rec.get('unit_price', 0)}")
                print(f"  库存: {rec.get('remaining_quantity', 0)}")
                print(f"  理由: {rec.get('reason', 'unknown')}")
                print()
                
        except json.JSONDecodeError:
            print("❌ 推荐API返回格式错误")
            print(f"响应内容: {response.text[:200]}")
    else:
        print(f"❌ 推荐API失败: {response.status_code}")
        print(f"响应内容: {response.text[:200]}")

def test_notifications(session):
    """测试通知功能"""
    print("=== 测试通知功能 ===")
    
    # 获取通知列表
    response = session.get(f'{BASE_URL}/agent/notifications')
    print(f"通知页面状态: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ 通知页面访问成功")
        
        # 获取未读通知数量
        response = session.get(f'{BASE_URL}/agent/api/notifications/unread_count')
        if response.status_code == 200:
            try:
                data = response.json()
                count = data.get('count', 0)
                print(f"未读通知数量: {count}")
                
                if count > 0:
                    # 尝试标记第一个通知为已读
                    print("尝试标记通知为已读...")
                    # 这里需要获取具体的通知ID，暂时跳过
                    
            except json.JSONDecodeError:
                print("❌ 未读通知数量API格式错误")
        else:
            print(f"❌ 未读通知数量API失败: {response.status_code}")
    else:
        print(f"❌ 通知页面访问失败: {response.status_code}")

def test_agent_execution(session):
    """测试Agent执行"""
    print("=== 测试Agent执行 ===")
    
    # 获取执行历史
    response = session.get(f'{BASE_URL}/agent/api/history/InventoryAlert?limit=3')
    print(f"执行历史API状态: {response.status_code}")
    
    if response.status_code == 200:
        try:
            history = response.json()
            print(f"✅ 执行历史数量: {len(history)}")
            
            for i, record in enumerate(history):
                print(f"记录 {i+1}:")
                print(f"  Agent: {record.get('agent_name', 'unknown')}")
                print(f"  状态: {record.get('status', 'unknown')}")
                print(f"  执行时间: {record.get('executed_at', 'unknown')}")
                print(f"  耗时: {record.get('execution_time', 0)}秒")
                print()
                
        except json.JSONDecodeError:
            print("❌ 执行历史API格式错误")
            print(f"响应内容: {response.text[:200]}")
    else:
        print(f"❌ 执行历史API失败: {response.status_code}")

def main():
    """主函数"""
    print("修复功能测试工具")
    print("=" * 50)
    
    # 登录
    session = login_as_admin()
    if not session:
        print("❌ 无法登录")
        return
    
    print("✅ 登录成功")
    
    # 测试各项功能
    test_recommendations(session)
    test_notifications(session)
    test_agent_execution(session)
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == '__main__':
    main()
