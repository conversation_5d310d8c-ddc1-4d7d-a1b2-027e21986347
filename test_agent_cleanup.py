#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Agent清理后的功能
"""

import time
from agents.inventory_alert_agent import InventoryAlertAgent
from agents.data_analysis_agent import DataAnalysisAgent
from services.agent_service import AgentService

def test_remaining_agents():
    """测试剩余的Agent功能"""
    print("=== 测试剩余Agent功能 ===")
    
    success_count = 0
    total_tests = 2
    
    # 测试库存预警Agent
    print("\n1. 测试库存预警Agent")
    try:
        agent = InventoryAlertAgent()
        start_time = time.time()
        result = agent.execute()
        execution_time = time.time() - start_time
        
        print(f"✅ 库存预警Agent执行成功")
        print(f"   执行时间: {execution_time:.2f}秒")
        print(f"   状态: {result.get('status', 'unknown')}")
        
        if 'alerts' in result:
            alerts = result['alerts']
            print(f"   生成预警: {len(alerts)} 条")
        
        success_count += 1
        
    except Exception as e:
        print(f"❌ 库存预警Agent执行失败: {e}")
    
    # 测试数据分析Agent
    print("\n2. 测试数据分析Agent")
    try:
        agent = DataAnalysisAgent()
        start_time = time.time()
        result = agent.execute()
        execution_time = time.time() - start_time
        
        print(f"✅ 数据分析Agent执行成功")
        print(f"   执行时间: {execution_time:.2f}秒")
        print(f"   状态: {result.get('status', 'unknown')}")
        
        # 检查分析结果
        expected_keys = ['usage_patterns', 'cost_analysis', 'trend_analysis', 'anomalies']
        for key in expected_keys:
            if key in result:
                print(f"   ✅ 包含 {key} 数据")
            else:
                print(f"   ⚠️ 缺少 {key} 数据")
        
        success_count += 1
        
    except Exception as e:
        print(f"❌ 数据分析Agent执行失败: {e}")
    
    print(f"\n测试结果: {success_count}/{total_tests} 通过")
    return success_count == total_tests

def test_agent_service():
    """测试Agent服务"""
    print("\n=== 测试Agent服务 ===")
    
    try:
        service = AgentService()
        
        # 测试分析数据获取
        print("1. 测试分析数据获取")
        analysis_data = service.get_analysis_data()
        
        if analysis_data:
            print("✅ 分析数据获取成功")
            
            # 检查基础统计
            stats = ['total_materials', 'total_requests', 'low_stock_count', 'total_value']
            for stat in stats:
                value = analysis_data.get(stat, 0)
                print(f"   {stat}: {value}")
            
            # 检查图表数据
            charts = ['category_distribution', 'monthly_trend', 'popular_materials']
            for chart in charts:
                data = analysis_data.get(chart, [])
                print(f"   {chart}: {len(data)} 条记录")
        else:
            print("⚠️ 分析数据为空")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent服务测试失败: {e}")
        return False

def test_removed_functionality():
    """测试已删除的功能"""
    print("\n=== 验证已删除功能 ===")
    
    # 检查SmartRecommendationAgent文件是否已删除
    import os
    
    smart_rec_file = "agents/smart_recommendation_agent.py"
    if not os.path.exists(smart_rec_file):
        print("✅ SmartRecommendationAgent文件已删除")
    else:
        print("❌ SmartRecommendationAgent文件仍然存在")
    
    # 检查推荐模板是否已删除
    rec_template = "templates/agent/recommendations.html"
    if not os.path.exists(rec_template):
        print("✅ 推荐页面模板已删除")
    else:
        print("❌ 推荐页面模板仍然存在")
    
    # 尝试导入SmartRecommendationAgent（应该失败）
    try:
        from agents.smart_recommendation_agent import SmartRecommendationAgent
        print("❌ SmartRecommendationAgent仍可导入")
    except ImportError:
        print("✅ SmartRecommendationAgent已无法导入")
    
    # 检查AgentService中是否还有推荐方法
    try:
        service = AgentService()
        if hasattr(service, 'get_user_recommendations'):
            print("❌ AgentService中仍有get_user_recommendations方法")
        else:
            print("✅ AgentService中已移除get_user_recommendations方法")
    except Exception as e:
        print(f"⚠️ 检查AgentService失败: {e}")

def test_database_cleanup():
    """测试数据库清理"""
    print("\n=== 验证数据库清理 ===")
    
    try:
        from dao.material_dao import MaterialDAO
        
        dao = MaterialDAO()
        db = dao.db
        
        # 检查user_recommendations表是否存在
        check_sql = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'user_recommendations'
        """
        result = db.execute_query(check_sql)
        
        if result and result[0]['count'] == 0:
            print("✅ user_recommendations表已删除")
        else:
            print("❌ user_recommendations表仍然存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库清理验证失败: {e}")
        return False

def main():
    """主函数"""
    print("Agent清理功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试剩余Agent功能
    test_results.append(test_remaining_agents())
    
    # 测试Agent服务
    test_results.append(test_agent_service())
    
    # 验证已删除功能
    test_removed_functionality()
    
    # 验证数据库清理
    test_results.append(test_database_cleanup())
    
    # 总结
    print("\n" + "=" * 60)
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"测试完成: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！Agent清理成功！")
        print("\n✅ 系统现在只保留:")
        print("   - 库存预警Agent (管理员专用)")
        print("   - 数据分析Agent (管理员专用)")
        print("   - 系统通知功能 (管理员专用)")
        print("\n❌ 已删除:")
        print("   - 智能推荐Agent")
        print("   - 普通用户的Agent访问权限")
        print("   - 推荐相关的数据库表和数据")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

if __name__ == '__main__':
    main()
