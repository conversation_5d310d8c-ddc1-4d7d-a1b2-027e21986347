#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查materials表结构
"""

from dao.database import Database

def check_materials_table():
    """检查materials表结构"""
    db = Database()
    
    try:
        # 检查表结构
        sql = "DESCRIBE materials"
        columns = db.execute_query(sql)
        print("materials表结构:")
        for col in columns:
            print(f"  {col['Field']}: {col['Type']}")
            
    except Exception as e:
        print(f"查询失败: {e}")

if __name__ == '__main__':
    check_materials_table()
